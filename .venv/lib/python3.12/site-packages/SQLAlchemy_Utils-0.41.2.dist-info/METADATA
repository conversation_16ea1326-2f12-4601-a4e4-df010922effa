Metadata-Version: 2.1
Name: SQLAlchemy-Utils
Version: 0.41.2
Summary: Various utility functions for SQLAlchemy.
Home-page: https://github.com/kvesteri/sqlalchemy-utils
Author: <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>
Author-email: <EMAIL>
License: BSD
Platform: any
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: Internet :: WWW/HTTP :: Dynamic Content
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.7
License-File: LICENSE
Requires-Dist: SQLAlchemy >=1.3
Requires-Dist: importlib-metadata ; python_version < "3.8"
Provides-Extra: arrow
Requires-Dist: arrow >=0.3.4 ; extra == 'arrow'
Provides-Extra: babel
Requires-Dist: Babel >=1.3 ; extra == 'babel'
Provides-Extra: color
Requires-Dist: colour >=0.0.4 ; extra == 'color'
Provides-Extra: encrypted
Requires-Dist: cryptography >=0.6 ; extra == 'encrypted'
Provides-Extra: intervals
Requires-Dist: intervals >=0.7.1 ; extra == 'intervals'
Provides-Extra: password
Requires-Dist: passlib <2.0,>=1.6 ; extra == 'password'
Provides-Extra: pendulum
Requires-Dist: pendulum >=2.0.5 ; extra == 'pendulum'
Provides-Extra: phone
Requires-Dist: phonenumbers >=5.9.2 ; extra == 'phone'
Provides-Extra: test
Requires-Dist: pytest ==7.4.4 ; extra == 'test'
Requires-Dist: Pygments >=1.2 ; extra == 'test'
Requires-Dist: Jinja2 >=2.3 ; extra == 'test'
Requires-Dist: docutils >=0.10 ; extra == 'test'
Requires-Dist: flexmock >=0.9.7 ; extra == 'test'
Requires-Dist: psycopg >=3.1.8 ; extra == 'test'
Requires-Dist: psycopg2 >=2.5.1 ; extra == 'test'
Requires-Dist: psycopg2cffi >=2.8.1 ; extra == 'test'
Requires-Dist: pg8000 >=1.12.4 ; extra == 'test'
Requires-Dist: pytz >=2014.2 ; extra == 'test'
Requires-Dist: python-dateutil >=2.6 ; extra == 'test'
Requires-Dist: pymysql ; extra == 'test'
Requires-Dist: flake8 >=2.4.0 ; extra == 'test'
Requires-Dist: isort >=4.2.2 ; extra == 'test'
Requires-Dist: pyodbc ; extra == 'test'
Requires-Dist: backports.zoneinfo ; (python_version < "3.9") and extra == 'test'
Provides-Extra: test_all
Requires-Dist: Babel >=1.3 ; extra == 'test_all'
Requires-Dist: Jinja2 >=2.3 ; extra == 'test_all'
Requires-Dist: Pygments >=1.2 ; extra == 'test_all'
Requires-Dist: arrow >=0.3.4 ; extra == 'test_all'
Requires-Dist: colour >=0.0.4 ; extra == 'test_all'
Requires-Dist: cryptography >=0.6 ; extra == 'test_all'
Requires-Dist: docutils >=0.10 ; extra == 'test_all'
Requires-Dist: flake8 >=2.4.0 ; extra == 'test_all'
Requires-Dist: flexmock >=0.9.7 ; extra == 'test_all'
Requires-Dist: furl >=0.4.1 ; extra == 'test_all'
Requires-Dist: intervals >=0.7.1 ; extra == 'test_all'
Requires-Dist: isort >=4.2.2 ; extra == 'test_all'
Requires-Dist: passlib <2.0,>=1.6 ; extra == 'test_all'
Requires-Dist: pendulum >=2.0.5 ; extra == 'test_all'
Requires-Dist: pg8000 >=1.12.4 ; extra == 'test_all'
Requires-Dist: phonenumbers >=5.9.2 ; extra == 'test_all'
Requires-Dist: psycopg2 >=2.5.1 ; extra == 'test_all'
Requires-Dist: psycopg2cffi >=2.8.1 ; extra == 'test_all'
Requires-Dist: psycopg >=3.1.8 ; extra == 'test_all'
Requires-Dist: pymysql ; extra == 'test_all'
Requires-Dist: pyodbc ; extra == 'test_all'
Requires-Dist: pytest ==7.4.4 ; extra == 'test_all'
Requires-Dist: python-dateutil ; extra == 'test_all'
Requires-Dist: python-dateutil >=2.6 ; extra == 'test_all'
Requires-Dist: pytz >=2014.2 ; extra == 'test_all'
Requires-Dist: backports.zoneinfo ; (python_version < "3.9") and extra == 'test_all'
Provides-Extra: timezone
Requires-Dist: python-dateutil ; extra == 'timezone'
Provides-Extra: url
Requires-Dist: furl >=0.4.1 ; extra == 'url'


SQLAlchemy-Utils
----------------

Various utility functions and custom data types for SQLAlchemy.
