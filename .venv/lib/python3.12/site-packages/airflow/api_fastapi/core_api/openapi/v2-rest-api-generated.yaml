openapi: 3.1.0
info:
  title: Airflow API
  description: Airflow API. All endpoints located under ``/api/v2`` can be used safely,
    are stable and backward compatible. Endpoints located under ``/ui`` are dedicated
    to the UI and are subject to breaking change depending on the need of the frontend.
    Users should not rely on those but use the public ones instead.
  version: '2'
paths:
  /api/v2/assets:
    get:
      tags:
      - Asset
      summary: Get Assets
      description: Get assets.
      operationId: get_assets
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 50
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 0
          title: Offset
      - name: name_pattern
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Name Pattern
      - name: uri_pattern
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Uri Pattern
      - name: dag_ids
        in: query
        required: false
        schema:
          type: array
          items:
            type: string
          title: Dag Ids
      - name: only_active
        in: query
        required: false
        schema:
          type: boolean
          default: true
          title: Only Active
      - name: order_by
        in: query
        required: false
        schema:
          type: string
          default: id
          title: Order By
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssetCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/assets/aliases:
    get:
      tags:
      - Asset
      summary: Get Asset Aliases
      description: Get asset aliases.
      operationId: get_asset_aliases
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 50
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 0
          title: Offset
      - name: name_pattern
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Name Pattern
      - name: order_by
        in: query
        required: false
        schema:
          type: string
          default: id
          title: Order By
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssetAliasCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/assets/aliases/{asset_alias_id}:
    get:
      tags:
      - Asset
      summary: Get Asset Alias
      description: Get an asset alias.
      operationId: get_asset_alias
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: asset_alias_id
        in: path
        required: true
        schema:
          type: integer
          title: Asset Alias Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/assets/events:
    get:
      tags:
      - Asset
      summary: Get Asset Events
      description: Get asset events.
      operationId: get_asset_events
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 50
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 0
          title: Offset
      - name: order_by
        in: query
        required: false
        schema:
          type: string
          default: timestamp
          title: Order By
      - name: asset_id
        in: query
        required: false
        schema:
          anyOf:
          - type: integer
          - type: 'null'
          title: Asset Id
      - name: source_dag_id
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Source Dag Id
      - name: source_task_id
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Source Task Id
      - name: source_run_id
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Source Run Id
      - name: source_map_index
        in: query
        required: false
        schema:
          anyOf:
          - type: integer
          - type: 'null'
          title: Source Map Index
      - name: timestamp_gte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Timestamp Gte
      - name: timestamp_lte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Timestamp Lte
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssetEventCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    post:
      tags:
      - Asset
      summary: Create Asset Event
      description: Create asset events.
      operationId: create_asset_event
      security:
      - OAuth2PasswordBearer: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAssetEventsBody'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssetEventResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/assets/{asset_id}/materialize:
    post:
      tags:
      - Asset
      summary: Materialize Asset
      description: Materialize an asset by triggering a DAG run that produces it.
      operationId: materialize_asset
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: asset_id
        in: path
        required: true
        schema:
          type: integer
          title: Asset Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DAGRunResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '409':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Conflict
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/assets/{asset_id}/queuedEvents:
    get:
      tags:
      - Asset
      summary: Get Asset Queued Events
      description: Get queued asset events for an asset.
      operationId: get_asset_queued_events
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: asset_id
        in: path
        required: true
        schema:
          type: integer
          title: Asset Id
      - name: before
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Before
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QueuedEventCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    delete:
      tags:
      - Asset
      summary: Delete Asset Queued Events
      description: Delete queued asset events for an asset.
      operationId: delete_asset_queued_events
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: asset_id
        in: path
        required: true
        schema:
          type: integer
          title: Asset Id
      - name: before
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Before
      responses:
        '204':
          description: Successful Response
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/assets/{asset_id}:
    get:
      tags:
      - Asset
      summary: Get Asset
      description: Get an asset.
      operationId: get_asset
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: asset_id
        in: path
        required: true
        schema:
          type: integer
          title: Asset Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssetResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/assets/queuedEvents:
    get:
      tags:
      - Asset
      summary: Get Dag Asset Queued Events
      description: Get queued asset events for a DAG.
      operationId: get_dag_asset_queued_events
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: before
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Before
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QueuedEventCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    delete:
      tags:
      - Asset
      summary: Delete Dag Asset Queued Events
      operationId: delete_dag_asset_queued_events
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: before
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Before
      responses:
        '204':
          description: Successful Response
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/assets/{asset_id}/queuedEvents:
    get:
      tags:
      - Asset
      summary: Get Dag Asset Queued Event
      description: Get a queued asset event for a DAG.
      operationId: get_dag_asset_queued_event
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: asset_id
        in: path
        required: true
        schema:
          type: integer
          title: Asset Id
      - name: before
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Before
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QueuedEventResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    delete:
      tags:
      - Asset
      summary: Delete Dag Asset Queued Event
      description: Delete a queued asset event for a DAG.
      operationId: delete_dag_asset_queued_event
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: asset_id
        in: path
        required: true
        schema:
          type: integer
          title: Asset Id
      - name: before
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Before
      responses:
        '204':
          description: Successful Response
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/backfills:
    get:
      tags:
      - Backfill
      summary: List Backfills
      operationId: list_backfills
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: query
        required: true
        schema:
          type: string
          title: Dag Id
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 50
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 0
          title: Offset
      - name: order_by
        in: query
        required: false
        schema:
          type: string
          default: id
          title: Order By
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BackfillCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    post:
      tags:
      - Backfill
      summary: Create Backfill
      operationId: create_backfill
      security:
      - OAuth2PasswordBearer: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BackfillPostBody'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BackfillResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '409':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Conflict
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/backfills/{backfill_id}:
    get:
      tags:
      - Backfill
      summary: Get Backfill
      operationId: get_backfill
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: backfill_id
        in: path
        required: true
        schema:
          type: integer
          minimum: 0
          title: Backfill Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BackfillResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/backfills/{backfill_id}/pause:
    put:
      tags:
      - Backfill
      summary: Pause Backfill
      operationId: pause_backfill
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: backfill_id
        in: path
        required: true
        schema:
          type: integer
          minimum: 0
          title: Backfill Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BackfillResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '409':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Conflict
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/backfills/{backfill_id}/unpause:
    put:
      tags:
      - Backfill
      summary: Unpause Backfill
      operationId: unpause_backfill
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: backfill_id
        in: path
        required: true
        schema:
          type: integer
          minimum: 0
          title: Backfill Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BackfillResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '409':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Conflict
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/backfills/{backfill_id}/cancel:
    put:
      tags:
      - Backfill
      summary: Cancel Backfill
      operationId: cancel_backfill
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: backfill_id
        in: path
        required: true
        schema:
          type: integer
          minimum: 0
          title: Backfill Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BackfillResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '409':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Conflict
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/backfills/dry_run:
    post:
      tags:
      - Backfill
      summary: Create Backfill Dry Run
      operationId: create_backfill_dry_run
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BackfillPostBody'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DryRunBackfillCollectionResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
        '409':
          description: Conflict
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v2/connections/{connection_id}:
    delete:
      tags:
      - Connection
      summary: Delete Connection
      description: Delete a connection entry.
      operationId: delete_connection
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: connection_id
        in: path
        required: true
        schema:
          type: string
          title: Connection Id
      responses:
        '204':
          description: Successful Response
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    get:
      tags:
      - Connection
      summary: Get Connection
      description: Get a connection entry.
      operationId: get_connection
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: connection_id
        in: path
        required: true
        schema:
          type: string
          title: Connection Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConnectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    patch:
      tags:
      - Connection
      summary: Patch Connection
      description: Update a connection entry.
      operationId: patch_connection
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: connection_id
        in: path
        required: true
        schema:
          type: string
          title: Connection Id
      - name: update_mask
        in: query
        required: false
        schema:
          anyOf:
          - type: array
            items:
              type: string
          - type: 'null'
          title: Update Mask
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConnectionBody'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConnectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/connections:
    get:
      tags:
      - Connection
      summary: Get Connections
      description: Get all connection entries.
      operationId: get_connections
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 50
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 0
          title: Offset
      - name: order_by
        in: query
        required: false
        schema:
          type: string
          default: id
          title: Order By
      - name: connection_id_pattern
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Connection Id Pattern
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConnectionCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    post:
      tags:
      - Connection
      summary: Post Connection
      description: Create connection entry.
      operationId: post_connection
      security:
      - OAuth2PasswordBearer: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConnectionBody'
      responses:
        '201':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConnectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '409':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Conflict
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    patch:
      tags:
      - Connection
      summary: Bulk Connections
      description: Bulk create, update, and delete connections.
      operationId: bulk_connections
      security:
      - OAuth2PasswordBearer: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkBody_ConnectionBody_'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/connections/test:
    post:
      tags:
      - Connection
      summary: Test Connection
      description: 'Test an API connection.


        This method first creates an in-memory transient conn_id & exports that to
        an env var,

        as some hook classes tries to find out the `conn` from their __init__ method
        & errors out if not found.

        It also deletes the conn id env connection after the test.'
      operationId: test_connection
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConnectionBody'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConnectionTestResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v2/connections/defaults:
    post:
      tags:
      - Connection
      summary: Create Default Connections
      description: Create default connections.
      operationId: create_default_connections
      responses:
        '204':
          description: Successful Response
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
      security:
      - OAuth2PasswordBearer: []
  /api/v2/dags/{dag_id}/dagRuns/{dag_run_id}:
    get:
      tags:
      - DagRun
      summary: Get Dag Run
      operationId: get_dag_run
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: dag_run_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Run Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DAGRunResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    delete:
      tags:
      - DagRun
      summary: Delete Dag Run
      description: Delete a DAG Run entry.
      operationId: delete_dag_run
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: dag_run_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Run Id
      responses:
        '204':
          description: Successful Response
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    patch:
      tags:
      - DagRun
      summary: Patch Dag Run
      description: Modify a DAG Run.
      operationId: patch_dag_run
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: dag_run_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Run Id
      - name: update_mask
        in: query
        required: false
        schema:
          anyOf:
          - type: array
            items:
              type: string
          - type: 'null'
          title: Update Mask
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DAGRunPatchBody'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DAGRunResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/dagRuns/{dag_run_id}/upstreamAssetEvents:
    get:
      tags:
      - DagRun
      summary: Get Upstream Asset Events
      description: If dag run is asset-triggered, return the asset events that triggered
        it.
      operationId: get_upstream_asset_events
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: dag_run_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Run Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssetEventCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/dagRuns/{dag_run_id}/clear:
    post:
      tags:
      - DagRun
      summary: Clear Dag Run
      operationId: clear_dag_run
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: dag_run_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Run Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DAGRunClearBody'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/TaskInstanceCollectionResponse'
                - $ref: '#/components/schemas/DAGRunResponse'
                title: Response Clear Dag Run
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/dagRuns:
    get:
      tags:
      - DagRun
      summary: Get Dag Runs
      description: 'Get all DAG Runs.


        This endpoint allows specifying `~` as the dag_id to retrieve Dag Runs for
        all DAGs.'
      operationId: get_dag_runs
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 50
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 0
          title: Offset
      - name: run_after_gte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Run After Gte
      - name: run_after_lte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Run After Lte
      - name: logical_date_gte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Logical Date Gte
      - name: logical_date_lte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Logical Date Lte
      - name: start_date_gte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Start Date Gte
      - name: start_date_lte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Start Date Lte
      - name: end_date_gte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: End Date Gte
      - name: end_date_lte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: End Date Lte
      - name: updated_at_gte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Updated At Gte
      - name: updated_at_lte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Updated At Lte
      - name: run_type
        in: query
        required: false
        schema:
          type: array
          items:
            type: string
          title: Run Type
      - name: state
        in: query
        required: false
        schema:
          type: array
          items:
            type: string
          title: State
      - name: order_by
        in: query
        required: false
        schema:
          type: string
          default: id
          title: Order By
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DAGRunCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    post:
      tags:
      - DagRun
      summary: Trigger Dag Run
      description: Trigger a DAG.
      operationId: trigger_dag_run
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          title: Dag Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TriggerDAGRunPostBody'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DAGRunResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '409':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Conflict
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/dagRuns/list:
    post:
      tags:
      - DagRun
      summary: Get List Dag Runs Batch
      description: Get a list of DAG Runs.
      operationId: get_list_dag_runs_batch
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          const: '~'
          type: string
          title: Dag Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DAGRunsBatchBody'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DAGRunCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dagSources/{dag_id}:
    get:
      tags:
      - DagSource
      summary: Get Dag Source
      description: Get source code using file token.
      operationId: get_dag_source
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: version_number
        in: query
        required: false
        schema:
          anyOf:
          - type: integer
          - type: 'null'
          title: Version Number
      - name: accept
        in: header
        required: false
        schema:
          type: string
          enum:
          - application/json
          - text/plain
          - '*/*'
          default: '*/*'
          title: Accept
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DAGSourceResponse'
            text/plain:
              schema:
                type: string
                example: dag code
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '406':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Acceptable
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dagStats:
    get:
      tags:
      - DagStats
      summary: Get Dag Stats
      description: Get Dag statistics.
      operationId: get_dag_stats
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_ids
        in: query
        required: false
        schema:
          type: array
          items:
            type: string
          title: Dag Ids
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DagStatsCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dagReports:
    get:
      tags:
      - DagReport
      summary: Get Dag Reports
      description: Get DAG report.
      operationId: get_dag_reports
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: subdir
        in: query
        required: true
        schema:
          type: string
          title: Subdir
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/config:
    get:
      tags:
      - Config
      summary: Get Config
      operationId: get_config
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: section
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Section
      - name: accept
        in: header
        required: false
        schema:
          type: string
          enum:
          - application/json
          - text/plain
          - '*/*'
          default: '*/*'
          title: Accept
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Config'
            text/plain:
              schema:
                type: string
                example: '[core]

                  dags_folder = /opt/airflow/dags

                  base_log_folder = /opt/airflow/logs


                  [smtp]

                  smtp_host = localhost

                  smtp_mail_from = <EMAIL>

                  '
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '406':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Acceptable
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/config/section/{section}/option/{option}:
    get:
      tags:
      - Config
      summary: Get Config Value
      operationId: get_config_value
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: section
        in: path
        required: true
        schema:
          type: string
          title: Section
      - name: option
        in: path
        required: true
        schema:
          type: string
          title: Option
      - name: accept
        in: header
        required: false
        schema:
          type: string
          enum:
          - application/json
          - text/plain
          - '*/*'
          default: '*/*'
          title: Accept
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Config'
            text/plain:
              schema:
                type: string
                example: '[core]

                  dags_folder = /opt/airflow/dags

                  base_log_folder = /opt/airflow/logs

                  '
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '406':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Acceptable
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dagWarnings:
    get:
      tags:
      - DagWarning
      summary: List Dag Warnings
      description: Get a list of DAG warnings.
      operationId: list_dag_warnings
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Dag Id
      - name: warning_type
        in: query
        required: false
        schema:
          anyOf:
          - $ref: '#/components/schemas/DagWarningType'
          - type: 'null'
          title: Warning Type
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 50
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 0
          title: Offset
      - name: order_by
        in: query
        required: false
        schema:
          type: string
          default: dag_id
          title: Order By
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DAGWarningCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags:
    get:
      tags:
      - DAG
      summary: Get Dags
      description: Get all DAGs.
      operationId: get_dags
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 50
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 0
          title: Offset
      - name: tags
        in: query
        required: false
        schema:
          type: array
          items:
            type: string
          title: Tags
      - name: tags_match_mode
        in: query
        required: false
        schema:
          anyOf:
          - enum:
            - any
            - all
            type: string
          - type: 'null'
          title: Tags Match Mode
      - name: owners
        in: query
        required: false
        schema:
          type: array
          items:
            type: string
          title: Owners
      - name: dag_id_pattern
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Dag Id Pattern
      - name: dag_display_name_pattern
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Dag Display Name Pattern
      - name: exclude_stale
        in: query
        required: false
        schema:
          type: boolean
          default: true
          title: Exclude Stale
      - name: paused
        in: query
        required: false
        schema:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Paused
      - name: last_dag_run_state
        in: query
        required: false
        schema:
          anyOf:
          - $ref: '#/components/schemas/DagRunState'
          - type: 'null'
          title: Last Dag Run State
      - name: dag_run_start_date_gte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Dag Run Start Date Gte
      - name: dag_run_start_date_lte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Dag Run Start Date Lte
      - name: dag_run_end_date_gte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Dag Run End Date Gte
      - name: dag_run_end_date_lte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Dag Run End Date Lte
      - name: dag_run_state
        in: query
        required: false
        schema:
          type: array
          items:
            type: string
          title: Dag Run State
      - name: order_by
        in: query
        required: false
        schema:
          type: string
          default: dag_id
          title: Order By
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DAGCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    patch:
      tags:
      - DAG
      summary: Patch Dags
      description: Patch multiple DAGs.
      operationId: patch_dags
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: update_mask
        in: query
        required: false
        schema:
          anyOf:
          - type: array
            items:
              type: string
          - type: 'null'
          title: Update Mask
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 50
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 0
          title: Offset
      - name: tags
        in: query
        required: false
        schema:
          type: array
          items:
            type: string
          title: Tags
      - name: tags_match_mode
        in: query
        required: false
        schema:
          anyOf:
          - enum:
            - any
            - all
            type: string
          - type: 'null'
          title: Tags Match Mode
      - name: owners
        in: query
        required: false
        schema:
          type: array
          items:
            type: string
          title: Owners
      - name: dag_id_pattern
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Dag Id Pattern
      - name: exclude_stale
        in: query
        required: false
        schema:
          type: boolean
          default: true
          title: Exclude Stale
      - name: paused
        in: query
        required: false
        schema:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Paused
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DAGPatchBody'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DAGCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}:
    get:
      tags:
      - DAG
      summary: Get Dag
      description: Get basic information about a DAG.
      operationId: get_dag
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DAGResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unprocessable Entity
    patch:
      tags:
      - DAG
      summary: Patch Dag
      description: Patch the specific DAG.
      operationId: patch_dag
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: update_mask
        in: query
        required: false
        schema:
          anyOf:
          - type: array
            items:
              type: string
          - type: 'null'
          title: Update Mask
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DAGPatchBody'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DAGResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    delete:
      tags:
      - DAG
      summary: Delete Dag
      description: Delete the specific DAG.
      operationId: delete_dag
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unprocessable Entity
  /api/v2/dags/{dag_id}/details:
    get:
      tags:
      - DAG
      summary: Get Dag Details
      description: Get details of DAG.
      operationId: get_dag_details
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DAGDetailsResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/eventLogs/{event_log_id}:
    get:
      tags:
      - Event Log
      summary: Get Event Log
      operationId: get_event_log
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: event_log_id
        in: path
        required: true
        schema:
          type: integer
          title: Event Log Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EventLogResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/eventLogs:
    get:
      tags:
      - Event Log
      summary: Get Event Logs
      description: Get all Event Logs.
      operationId: get_event_logs
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 50
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 0
          title: Offset
      - name: order_by
        in: query
        required: false
        schema:
          type: string
          default: id
          title: Order By
      - name: dag_id
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Dag Id
      - name: task_id
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Task Id
      - name: run_id
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Run Id
      - name: map_index
        in: query
        required: false
        schema:
          anyOf:
          - type: integer
          - type: 'null'
          title: Map Index
      - name: try_number
        in: query
        required: false
        schema:
          anyOf:
          - type: integer
          - type: 'null'
          title: Try Number
      - name: owner
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Owner
      - name: event
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Event
      - name: excluded_events
        in: query
        required: false
        schema:
          anyOf:
          - type: array
            items:
              type: string
          - type: 'null'
          title: Excluded Events
      - name: included_events
        in: query
        required: false
        schema:
          anyOf:
          - type: array
            items:
              type: string
          - type: 'null'
          title: Included Events
      - name: before
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Before
      - name: after
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: After
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EventLogCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/dagRuns/{dag_run_id}/taskInstances/{task_id}/links:
    get:
      tags:
      - Extra Links
      - Task Instance
      summary: Get Extra Links
      description: Get extra links for task instance.
      operationId: get_extra_links
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: dag_run_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Run Id
      - name: task_id
        in: path
        required: true
        schema:
          type: string
          title: Task Id
      - name: map_index
        in: query
        required: false
        schema:
          type: integer
          default: -1
          title: Map Index
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExtraLinkCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/importErrors/{import_error_id}:
    get:
      tags:
      - Import Error
      summary: Get Import Error
      description: Get an import error.
      operationId: get_import_error
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: import_error_id
        in: path
        required: true
        schema:
          type: integer
          title: Import Error Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImportErrorResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/importErrors:
    get:
      tags:
      - Import Error
      summary: Get Import Errors
      description: Get all import errors.
      operationId: get_import_errors
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 50
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 0
          title: Offset
      - name: order_by
        in: query
        required: false
        schema:
          type: string
          default: id
          title: Order By
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImportErrorCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/jobs:
    get:
      tags:
      - Job
      summary: Get Jobs
      description: Get all jobs.
      operationId: get_jobs
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: is_alive
        in: query
        required: false
        schema:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Is Alive
      - name: start_date_gte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Start Date Gte
      - name: start_date_lte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Start Date Lte
      - name: end_date_gte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: End Date Gte
      - name: end_date_lte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: End Date Lte
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 50
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 0
          title: Offset
      - name: order_by
        in: query
        required: false
        schema:
          type: string
          default: id
          title: Order By
      - name: job_state
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Job State
      - name: job_type
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Job Type
      - name: hostname
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Hostname
      - name: executor_class
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Executor Class
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/plugins:
    get:
      tags:
      - Plugin
      summary: Get Plugins
      operationId: get_plugins
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 50
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 0
          title: Offset
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PluginCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/pools/{pool_name}:
    delete:
      tags:
      - Pool
      summary: Delete Pool
      description: Delete a pool entry.
      operationId: delete_pool
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: pool_name
        in: path
        required: true
        schema:
          type: string
          title: Pool Name
      responses:
        '204':
          description: Successful Response
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    get:
      tags:
      - Pool
      summary: Get Pool
      description: Get a pool.
      operationId: get_pool
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: pool_name
        in: path
        required: true
        schema:
          type: string
          title: Pool Name
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PoolResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    patch:
      tags:
      - Pool
      summary: Patch Pool
      description: Update a Pool.
      operationId: patch_pool
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: pool_name
        in: path
        required: true
        schema:
          type: string
          title: Pool Name
      - name: update_mask
        in: query
        required: false
        schema:
          anyOf:
          - type: array
            items:
              type: string
          - type: 'null'
          title: Update Mask
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PoolPatchBody'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PoolResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/pools:
    get:
      tags:
      - Pool
      summary: Get Pools
      description: Get all pools entries.
      operationId: get_pools
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 50
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 0
          title: Offset
      - name: order_by
        in: query
        required: false
        schema:
          type: string
          default: id
          title: Order By
      - name: pool_name_pattern
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Pool Name Pattern
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PoolCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    post:
      tags:
      - Pool
      summary: Post Pool
      description: Create a Pool.
      operationId: post_pool
      security:
      - OAuth2PasswordBearer: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PoolBody'
      responses:
        '201':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PoolResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '409':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Conflict
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    patch:
      tags:
      - Pool
      summary: Bulk Pools
      description: Bulk create, update, and delete pools.
      operationId: bulk_pools
      security:
      - OAuth2PasswordBearer: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkBody_PoolBody_'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/providers:
    get:
      tags:
      - Provider
      summary: Get Providers
      description: Get providers.
      operationId: get_providers
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 50
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 0
          title: Offset
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProviderCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/dagRuns/{dag_run_id}/taskInstances/{task_id}/xcomEntries/{xcom_key}:
    get:
      tags:
      - XCom
      summary: Get Xcom Entry
      description: Get an XCom entry.
      operationId: get_xcom_entry
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: task_id
        in: path
        required: true
        schema:
          type: string
          title: Task Id
      - name: dag_run_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Run Id
      - name: xcom_key
        in: path
        required: true
        schema:
          type: string
          title: Xcom Key
      - name: map_index
        in: query
        required: false
        schema:
          type: integer
          minimum: -1
          default: -1
          title: Map Index
      - name: deserialize
        in: query
        required: false
        schema:
          type: boolean
          default: false
          title: Deserialize
      - name: stringify
        in: query
        required: false
        schema:
          type: boolean
          default: false
          title: Stringify
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/XComResponseNative'
                - $ref: '#/components/schemas/XComResponseString'
                title: Response Get Xcom Entry
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    patch:
      tags:
      - XCom
      summary: Update Xcom Entry
      description: Update an existing XCom entry.
      operationId: update_xcom_entry
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: task_id
        in: path
        required: true
        schema:
          type: string
          title: Task Id
      - name: dag_run_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Run Id
      - name: xcom_key
        in: path
        required: true
        schema:
          type: string
          title: Xcom Key
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/XComUpdateBody'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/XComResponseNative'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/dagRuns/{dag_run_id}/taskInstances/{task_id}/xcomEntries:
    get:
      tags:
      - XCom
      summary: Get Xcom Entries
      description: 'Get all XCom entries.


        This endpoint allows specifying `~` as the dag_id, dag_run_id, task_id to
        retrieve XCom entries for all DAGs.'
      operationId: get_xcom_entries
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: dag_run_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Run Id
      - name: task_id
        in: path
        required: true
        schema:
          type: string
          title: Task Id
      - name: xcom_key
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Xcom Key
      - name: map_index
        in: query
        required: false
        schema:
          anyOf:
          - type: integer
            minimum: -1
          - type: 'null'
          title: Map Index
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 50
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 0
          title: Offset
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/XComCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    post:
      tags:
      - XCom
      summary: Create Xcom Entry
      description: Create an XCom entry.
      operationId: create_xcom_entry
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: task_id
        in: path
        required: true
        schema:
          type: string
          title: Task Id
      - name: dag_run_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Run Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/XComCreateBody'
      responses:
        '201':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/XComResponseNative'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/dagRuns/{dag_run_id}/taskInstances/{task_id}:
    get:
      tags:
      - Task Instance
      summary: Get Task Instance
      description: Get task instance.
      operationId: get_task_instance
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: dag_run_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Run Id
      - name: task_id
        in: path
        required: true
        schema:
          type: string
          title: Task Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskInstanceResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    patch:
      tags:
      - Task Instance
      summary: Patch Task Instance
      description: Update a task instance.
      operationId: patch_task_instance
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: dag_run_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Run Id
      - name: task_id
        in: path
        required: true
        schema:
          type: string
          title: Task Id
      - name: map_index
        in: query
        required: false
        schema:
          anyOf:
          - type: integer
          - type: 'null'
          title: Map Index
      - name: update_mask
        in: query
        required: false
        schema:
          anyOf:
          - type: array
            items:
              type: string
          - type: 'null'
          title: Update Mask
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchTaskInstanceBody'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskInstanceCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '409':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Conflict
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/dagRuns/{dag_run_id}/taskInstances/{task_id}/listMapped:
    get:
      tags:
      - Task Instance
      summary: Get Mapped Task Instances
      description: Get list of mapped task instances.
      operationId: get_mapped_task_instances
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: dag_run_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Run Id
      - name: task_id
        in: path
        required: true
        schema:
          type: string
          title: Task Id
      - name: run_after_gte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Run After Gte
      - name: run_after_lte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Run After Lte
      - name: logical_date_gte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Logical Date Gte
      - name: logical_date_lte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Logical Date Lte
      - name: start_date_gte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Start Date Gte
      - name: start_date_lte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Start Date Lte
      - name: end_date_gte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: End Date Gte
      - name: end_date_lte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: End Date Lte
      - name: updated_at_gte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Updated At Gte
      - name: updated_at_lte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Updated At Lte
      - name: duration_gte
        in: query
        required: false
        schema:
          anyOf:
          - type: number
          - type: 'null'
          title: Duration Gte
      - name: duration_lte
        in: query
        required: false
        schema:
          anyOf:
          - type: number
          - type: 'null'
          title: Duration Lte
      - name: state
        in: query
        required: false
        schema:
          type: array
          items:
            type: string
          title: State
      - name: pool
        in: query
        required: false
        schema:
          type: array
          items:
            type: string
          title: Pool
      - name: queue
        in: query
        required: false
        schema:
          type: array
          items:
            type: string
          title: Queue
      - name: executor
        in: query
        required: false
        schema:
          type: array
          items:
            type: string
          title: Executor
      - name: version_number
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
          title: Version Number
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 50
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 0
          title: Offset
      - name: order_by
        in: query
        required: false
        schema:
          type: string
          default: map_index
          title: Order By
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskInstanceCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/dagRuns/{dag_run_id}/taskInstances/{task_id}/{map_index}/dependencies:
    get:
      tags:
      - Task Instance
      summary: Get Task Instance Dependencies
      description: Get dependencies blocking task from getting scheduled.
      operationId: get_task_instance_dependencies_by_map_index
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: dag_run_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Run Id
      - name: task_id
        in: path
        required: true
        schema:
          type: string
          title: Task Id
      - name: map_index
        in: path
        required: true
        schema:
          type: integer
          title: Map Index
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskDependencyCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/dagRuns/{dag_run_id}/taskInstances/{task_id}/dependencies:
    get:
      tags:
      - Task Instance
      summary: Get Task Instance Dependencies
      description: Get dependencies blocking task from getting scheduled.
      operationId: get_task_instance_dependencies
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: dag_run_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Run Id
      - name: task_id
        in: path
        required: true
        schema:
          type: string
          title: Task Id
      - name: map_index
        in: query
        required: false
        schema:
          type: integer
          default: -1
          title: Map Index
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskDependencyCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/dagRuns/{dag_run_id}/taskInstances/{task_id}/tries:
    get:
      tags:
      - Task Instance
      summary: Get Task Instance Tries
      description: Get list of task instances history.
      operationId: get_task_instance_tries
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: dag_run_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Run Id
      - name: task_id
        in: path
        required: true
        schema:
          type: string
          title: Task Id
      - name: map_index
        in: query
        required: false
        schema:
          type: integer
          default: -1
          title: Map Index
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskInstanceHistoryCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/dagRuns/{dag_run_id}/taskInstances/{task_id}/{map_index}/tries:
    get:
      tags:
      - Task Instance
      summary: Get Mapped Task Instance Tries
      operationId: get_mapped_task_instance_tries
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: dag_run_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Run Id
      - name: task_id
        in: path
        required: true
        schema:
          type: string
          title: Task Id
      - name: map_index
        in: path
        required: true
        schema:
          type: integer
          title: Map Index
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskInstanceHistoryCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/dagRuns/{dag_run_id}/taskInstances/{task_id}/{map_index}:
    get:
      tags:
      - Task Instance
      summary: Get Mapped Task Instance
      description: Get task instance.
      operationId: get_mapped_task_instance
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: dag_run_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Run Id
      - name: task_id
        in: path
        required: true
        schema:
          type: string
          title: Task Id
      - name: map_index
        in: path
        required: true
        schema:
          type: integer
          title: Map Index
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskInstanceResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    patch:
      tags:
      - Task Instance
      summary: Patch Task Instance
      description: Update a task instance.
      operationId: patch_task_instance_by_map_index
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: dag_run_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Run Id
      - name: task_id
        in: path
        required: true
        schema:
          type: string
          title: Task Id
      - name: map_index
        in: path
        required: true
        schema:
          anyOf:
          - type: integer
          - type: 'null'
          title: Map Index
      - name: update_mask
        in: query
        required: false
        schema:
          anyOf:
          - type: array
            items:
              type: string
          - type: 'null'
          title: Update Mask
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchTaskInstanceBody'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskInstanceCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '409':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Conflict
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/dagRuns/{dag_run_id}/taskInstances:
    get:
      tags:
      - Task Instance
      summary: Get Task Instances
      description: 'Get list of task instances.


        This endpoint allows specifying `~` as the dag_id, dag_run_id to retrieve
        Task Instances for all DAGs

        and DAG runs.'
      operationId: get_task_instances
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: dag_run_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Run Id
      - name: task_id
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Task Id
      - name: run_after_gte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Run After Gte
      - name: run_after_lte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Run After Lte
      - name: logical_date_gte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Logical Date Gte
      - name: logical_date_lte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Logical Date Lte
      - name: start_date_gte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Start Date Gte
      - name: start_date_lte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Start Date Lte
      - name: end_date_gte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: End Date Gte
      - name: end_date_lte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: End Date Lte
      - name: updated_at_gte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Updated At Gte
      - name: updated_at_lte
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Updated At Lte
      - name: duration_gte
        in: query
        required: false
        schema:
          anyOf:
          - type: number
          - type: 'null'
          title: Duration Gte
      - name: duration_lte
        in: query
        required: false
        schema:
          anyOf:
          - type: number
          - type: 'null'
          title: Duration Lte
      - name: task_display_name_pattern
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Task Display Name Pattern
      - name: state
        in: query
        required: false
        schema:
          type: array
          items:
            type: string
          title: State
      - name: pool
        in: query
        required: false
        schema:
          type: array
          items:
            type: string
          title: Pool
      - name: queue
        in: query
        required: false
        schema:
          type: array
          items:
            type: string
          title: Queue
      - name: executor
        in: query
        required: false
        schema:
          type: array
          items:
            type: string
          title: Executor
      - name: version_number
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
          title: Version Number
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 50
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 0
          title: Offset
      - name: order_by
        in: query
        required: false
        schema:
          type: string
          default: map_index
          title: Order By
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskInstanceCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/dagRuns/{dag_run_id}/taskInstances/list:
    post:
      tags:
      - Task Instance
      summary: Get Task Instances Batch
      description: Get list of task instances.
      operationId: get_task_instances_batch
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          const: '~'
          type: string
          title: Dag Id
      - name: dag_run_id
        in: path
        required: true
        schema:
          const: '~'
          type: string
          title: Dag Run Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TaskInstancesBatchBody'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskInstanceCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/dagRuns/{dag_run_id}/taskInstances/{task_id}/tries/{task_try_number}:
    get:
      tags:
      - Task Instance
      summary: Get Task Instance Try Details
      description: Get task instance details by try number.
      operationId: get_task_instance_try_details
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: dag_run_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Run Id
      - name: task_id
        in: path
        required: true
        schema:
          type: string
          title: Task Id
      - name: task_try_number
        in: path
        required: true
        schema:
          type: integer
          title: Task Try Number
      - name: map_index
        in: query
        required: false
        schema:
          type: integer
          default: -1
          title: Map Index
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskInstanceHistoryResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/dagRuns/{dag_run_id}/taskInstances/{task_id}/{map_index}/tries/{task_try_number}:
    get:
      tags:
      - Task Instance
      summary: Get Mapped Task Instance Try Details
      operationId: get_mapped_task_instance_try_details
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: dag_run_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Run Id
      - name: task_id
        in: path
        required: true
        schema:
          type: string
          title: Task Id
      - name: task_try_number
        in: path
        required: true
        schema:
          type: integer
          title: Task Try Number
      - name: map_index
        in: path
        required: true
        schema:
          type: integer
          title: Map Index
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskInstanceHistoryResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/clearTaskInstances:
    post:
      tags:
      - Task Instance
      summary: Post Clear Task Instances
      description: Clear task instances.
      operationId: post_clear_task_instances
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClearTaskInstancesBody'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskInstanceCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/dagRuns/{dag_run_id}/taskInstances/{task_id}/{map_index}/dry_run:
    patch:
      tags:
      - Task Instance
      summary: Patch Task Instance Dry Run
      description: Update a task instance dry_run mode.
      operationId: patch_task_instance_dry_run_by_map_index
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: dag_run_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Run Id
      - name: task_id
        in: path
        required: true
        schema:
          type: string
          title: Task Id
      - name: map_index
        in: path
        required: true
        schema:
          anyOf:
          - type: integer
          - type: 'null'
          title: Map Index
      - name: update_mask
        in: query
        required: false
        schema:
          anyOf:
          - type: array
            items:
              type: string
          - type: 'null'
          title: Update Mask
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchTaskInstanceBody'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskInstanceCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/dagRuns/{dag_run_id}/taskInstances/{task_id}/dry_run:
    patch:
      tags:
      - Task Instance
      summary: Patch Task Instance Dry Run
      description: Update a task instance dry_run mode.
      operationId: patch_task_instance_dry_run
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: dag_run_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Run Id
      - name: task_id
        in: path
        required: true
        schema:
          type: string
          title: Task Id
      - name: map_index
        in: query
        required: false
        schema:
          anyOf:
          - type: integer
          - type: 'null'
          title: Map Index
      - name: update_mask
        in: query
        required: false
        schema:
          anyOf:
          - type: array
            items:
              type: string
          - type: 'null'
          title: Update Mask
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchTaskInstanceBody'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskInstanceCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/tasks:
    get:
      tags:
      - Task
      summary: Get Tasks
      description: Get tasks for DAG.
      operationId: get_tasks
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: order_by
        in: query
        required: false
        schema:
          type: string
          default: task_id
          title: Order By
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/tasks/{task_id}:
    get:
      tags:
      - Task
      summary: Get Task
      description: Get simplified representation of a task.
      operationId: get_task
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: task_id
        in: path
        required: true
        schema:
          title: Task Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/variables/{variable_key}:
    delete:
      tags:
      - Variable
      summary: Delete Variable
      description: Delete a variable entry.
      operationId: delete_variable
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: variable_key
        in: path
        required: true
        schema:
          type: string
          title: Variable Key
      responses:
        '204':
          description: Successful Response
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    get:
      tags:
      - Variable
      summary: Get Variable
      description: Get a variable entry.
      operationId: get_variable
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: variable_key
        in: path
        required: true
        schema:
          type: string
          title: Variable Key
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VariableResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    patch:
      tags:
      - Variable
      summary: Patch Variable
      description: Update a variable by key.
      operationId: patch_variable
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: variable_key
        in: path
        required: true
        schema:
          type: string
          title: Variable Key
      - name: update_mask
        in: query
        required: false
        schema:
          anyOf:
          - type: array
            items:
              type: string
          - type: 'null'
          title: Update Mask
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VariableBody'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VariableResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/variables:
    get:
      tags:
      - Variable
      summary: Get Variables
      description: Get all Variables entries.
      operationId: get_variables
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 50
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 0
          title: Offset
      - name: order_by
        in: query
        required: false
        schema:
          type: string
          default: id
          title: Order By
      - name: variable_key_pattern
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Variable Key Pattern
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VariableCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    post:
      tags:
      - Variable
      summary: Post Variable
      description: Create a variable.
      operationId: post_variable
      security:
      - OAuth2PasswordBearer: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VariableBody'
      responses:
        '201':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VariableResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '409':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Conflict
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    patch:
      tags:
      - Variable
      summary: Bulk Variables
      description: Bulk create, update, and delete variables.
      operationId: bulk_variables
      security:
      - OAuth2PasswordBearer: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkBody_VariableBody_'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/dagRuns/{dag_run_id}/taskInstances/{task_id}/logs/{try_number}:
    get:
      tags:
      - Task Instance
      summary: Get Log
      description: Get logs for a specific task instance.
      operationId: get_log
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: dag_run_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Run Id
      - name: task_id
        in: path
        required: true
        schema:
          type: string
          title: Task Id
      - name: try_number
        in: path
        required: true
        schema:
          type: integer
          minimum: 0
          title: Try Number
      - name: full_content
        in: query
        required: false
        schema:
          type: boolean
          default: false
          title: Full Content
      - name: map_index
        in: query
        required: false
        schema:
          type: integer
          default: -1
          title: Map Index
      - name: token
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Token
      - name: accept
        in: header
        required: false
        schema:
          type: string
          enum:
          - application/json
          - application/x-ndjson
          - '*/*'
          default: '*/*'
          title: Accept
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskInstancesLogResponse'
            application/x-ndjson:
              schema:
                type: string
                example: '{"content": "content"}

                  {"content": "content"}

                  '
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/parseDagFile/{file_token}:
    put:
      tags:
      - DAG Parsing
      summary: Reparse Dag File
      description: Request re-parsing a DAG file.
      operationId: reparse_dag_file
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: file_token
        in: path
        required: true
        schema:
          type: string
          title: File Token
      responses:
        '201':
          description: Successful Response
          content:
            application/json:
              schema:
                type: 'null'
                title: Response Reparse Dag File
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dagTags:
    get:
      tags:
      - DAG
      summary: Get Dag Tags
      description: Get all DAG tags.
      operationId: get_dag_tags
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 50
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 0
          title: Offset
      - name: order_by
        in: query
        required: false
        schema:
          type: string
          default: name
          title: Order By
      - name: tag_name_pattern
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Tag Name Pattern
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DAGTagCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/dagVersions/{version_number}:
    get:
      tags:
      - DagVersion
      summary: Get Dag Version
      description: Get one Dag Version.
      operationId: get_dag_version
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: version_number
        in: path
        required: true
        schema:
          type: integer
          title: Version Number
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DagVersionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/dags/{dag_id}/dagVersions:
    get:
      tags:
      - DagVersion
      summary: Get Dag Versions
      description: 'Get all DAG Versions.


        This endpoint allows specifying `~` as the dag_id to retrieve DAG Versions
        for all DAGs.'
      operationId: get_dag_versions
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: dag_id
        in: path
        required: true
        schema:
          type: string
          title: Dag Id
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 50
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 0
          title: Offset
      - name: version_number
        in: query
        required: false
        schema:
          type: integer
          title: Version Number
      - name: bundle_name
        in: query
        required: false
        schema:
          type: string
          title: Bundle Name
      - name: bundle_version
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Bundle Version
      - name: order_by
        in: query
        required: false
        schema:
          type: string
          default: id
          title: Order By
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DAGVersionCollectionResponse'
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Not Found
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/monitor/health:
    get:
      tags:
      - Monitor
      summary: Get Health
      operationId: get_health
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthInfoResponse'
  /api/v2/version:
    get:
      tags:
      - Version
      summary: Get Version
      description: Get version information.
      operationId: get_version
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VersionInfo'
  /api/v2/auth/login:
    get:
      tags:
      - Login
      summary: Login
      description: Redirect to the login URL depending on the AuthManager configured.
      operationId: login
      parameters:
      - name: next
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Next
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '307':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Temporary Redirect
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v2/auth/logout:
    get:
      tags:
      - Login
      summary: Logout
      description: Logout the user.
      operationId: logout
      parameters:
      - name: next
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Next
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '307':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPExceptionResponse'
          description: Temporary Redirect
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
components:
  schemas:
    AppBuilderMenuItemResponse:
      properties:
        name:
          type: string
          title: Name
        href:
          anyOf:
          - type: string
          - type: 'null'
          title: Href
        category:
          anyOf:
          - type: string
          - type: 'null'
          title: Category
      additionalProperties: true
      type: object
      required:
      - name
      title: AppBuilderMenuItemResponse
      description: Serializer for AppBuilder Menu Item responses.
    AppBuilderViewResponse:
      properties:
        name:
          anyOf:
          - type: string
          - type: 'null'
          title: Name
        category:
          anyOf:
          - type: string
          - type: 'null'
          title: Category
        view:
          anyOf:
          - type: string
          - type: 'null'
          title: View
        label:
          anyOf:
          - type: string
          - type: 'null'
          title: Label
      additionalProperties: true
      type: object
      title: AppBuilderViewResponse
      description: Serializer for AppBuilder View responses.
    AssetAliasCollectionResponse:
      properties:
        asset_aliases:
          items:
            $ref: '#/components/schemas/AssetAliasResponse'
          type: array
          title: Asset Aliases
        total_entries:
          type: integer
          title: Total Entries
      type: object
      required:
      - asset_aliases
      - total_entries
      title: AssetAliasCollectionResponse
      description: Asset alias collection response.
    AssetAliasResponse:
      properties:
        id:
          type: integer
          title: Id
        name:
          type: string
          title: Name
        group:
          type: string
          title: Group
      type: object
      required:
      - id
      - name
      - group
      title: AssetAliasResponse
      description: Asset alias serializer for responses.
    AssetCollectionResponse:
      properties:
        assets:
          items:
            $ref: '#/components/schemas/AssetResponse'
          type: array
          title: Assets
        total_entries:
          type: integer
          title: Total Entries
      type: object
      required:
      - assets
      - total_entries
      title: AssetCollectionResponse
      description: Asset collection response.
    AssetEventCollectionResponse:
      properties:
        asset_events:
          items:
            $ref: '#/components/schemas/AssetEventResponse'
          type: array
          title: Asset Events
        total_entries:
          type: integer
          title: Total Entries
      type: object
      required:
      - asset_events
      - total_entries
      title: AssetEventCollectionResponse
      description: Asset event collection response.
    AssetEventResponse:
      properties:
        id:
          type: integer
          title: Id
        asset_id:
          type: integer
          title: Asset Id
        uri:
          anyOf:
          - type: string
          - type: 'null'
          title: Uri
        name:
          anyOf:
          - type: string
          - type: 'null'
          title: Name
        group:
          anyOf:
          - type: string
          - type: 'null'
          title: Group
        extra:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Extra
        source_task_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Source Task Id
        source_dag_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Source Dag Id
        source_run_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Source Run Id
        source_map_index:
          type: integer
          title: Source Map Index
        created_dagruns:
          items:
            $ref: '#/components/schemas/DagRunAssetReference'
          type: array
          title: Created Dagruns
        timestamp:
          type: string
          format: date-time
          title: Timestamp
      type: object
      required:
      - id
      - asset_id
      - source_map_index
      - created_dagruns
      - timestamp
      title: AssetEventResponse
      description: Asset event serializer for responses.
    AssetResponse:
      properties:
        id:
          type: integer
          title: Id
        name:
          type: string
          title: Name
        uri:
          type: string
          title: Uri
        group:
          type: string
          title: Group
        extra:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Extra
        created_at:
          type: string
          format: date-time
          title: Created At
        updated_at:
          type: string
          format: date-time
          title: Updated At
        consuming_dags:
          items:
            $ref: '#/components/schemas/DagScheduleAssetReference'
          type: array
          title: Consuming Dags
        producing_tasks:
          items:
            $ref: '#/components/schemas/TaskOutletAssetReference'
          type: array
          title: Producing Tasks
        aliases:
          items:
            $ref: '#/components/schemas/AssetAliasResponse'
          type: array
          title: Aliases
      type: object
      required:
      - id
      - name
      - uri
      - group
      - created_at
      - updated_at
      - consuming_dags
      - producing_tasks
      - aliases
      title: AssetResponse
      description: Asset serializer for responses.
    BackfillCollectionResponse:
      properties:
        backfills:
          items:
            $ref: '#/components/schemas/BackfillResponse'
          type: array
          title: Backfills
        total_entries:
          type: integer
          title: Total Entries
      type: object
      required:
      - backfills
      - total_entries
      title: BackfillCollectionResponse
      description: Backfill Collection serializer for responses.
    BackfillPostBody:
      properties:
        dag_id:
          type: string
          title: Dag Id
        from_date:
          type: string
          format: date-time
          title: From Date
        to_date:
          type: string
          format: date-time
          title: To Date
        run_backwards:
          type: boolean
          title: Run Backwards
          default: false
        dag_run_conf:
          additionalProperties: true
          type: object
          title: Dag Run Conf
          default: {}
        reprocess_behavior:
          $ref: '#/components/schemas/ReprocessBehavior'
          default: none
        max_active_runs:
          type: integer
          title: Max Active Runs
          default: 10
      additionalProperties: false
      type: object
      required:
      - dag_id
      - from_date
      - to_date
      title: BackfillPostBody
      description: Object used for create backfill request.
    BackfillResponse:
      properties:
        id:
          type: integer
          minimum: 0.0
          title: Id
        dag_id:
          type: string
          title: Dag Id
        from_date:
          type: string
          format: date-time
          title: From Date
        to_date:
          type: string
          format: date-time
          title: To Date
        dag_run_conf:
          additionalProperties: true
          type: object
          title: Dag Run Conf
        is_paused:
          type: boolean
          title: Is Paused
        reprocess_behavior:
          $ref: '#/components/schemas/ReprocessBehavior'
        max_active_runs:
          type: integer
          title: Max Active Runs
        created_at:
          type: string
          format: date-time
          title: Created At
        completed_at:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Completed At
        updated_at:
          type: string
          format: date-time
          title: Updated At
      type: object
      required:
      - id
      - dag_id
      - from_date
      - to_date
      - dag_run_conf
      - is_paused
      - reprocess_behavior
      - max_active_runs
      - created_at
      - completed_at
      - updated_at
      title: BackfillResponse
      description: Base serializer for Backfill.
    BaseInfoResponse:
      properties:
        status:
          anyOf:
          - type: string
          - type: 'null'
          title: Status
      type: object
      required:
      - status
      title: BaseInfoResponse
      description: Base info serializer for responses.
    BulkActionNotOnExistence:
      type: string
      enum:
      - fail
      - skip
      title: BulkActionNotOnExistence
      description: Bulk Action to be taken if the entity does not exist.
    BulkActionOnExistence:
      type: string
      enum:
      - fail
      - skip
      - overwrite
      title: BulkActionOnExistence
      description: Bulk Action to be taken if the entity already exists or not.
    BulkActionResponse:
      properties:
        success:
          items:
            type: string
          type: array
          title: Success
          description: A list of unique id/key representing successful operations.
          default: []
        errors:
          items:
            additionalProperties: true
            type: object
          type: array
          title: Errors
          description: A list of errors encountered during the operation, each containing
            details about the issue.
          default: []
      type: object
      title: BulkActionResponse
      description: 'Serializer for individual bulk action responses.


        Represents the outcome of a single bulk operation (create, update, or delete).

        The response includes a list of successful keys and any errors encountered
        during the operation.

        This structure helps users understand which key actions succeeded and which
        failed.'
    BulkBody_ConnectionBody_:
      properties:
        actions:
          items:
            oneOf:
            - $ref: '#/components/schemas/BulkCreateAction_ConnectionBody_'
            - $ref: '#/components/schemas/BulkUpdateAction_ConnectionBody_'
            - $ref: '#/components/schemas/BulkDeleteAction_ConnectionBody_'
          type: array
          title: Actions
      additionalProperties: false
      type: object
      required:
      - actions
      title: BulkBody[ConnectionBody]
    BulkBody_PoolBody_:
      properties:
        actions:
          items:
            oneOf:
            - $ref: '#/components/schemas/BulkCreateAction_PoolBody_'
            - $ref: '#/components/schemas/BulkUpdateAction_PoolBody_'
            - $ref: '#/components/schemas/BulkDeleteAction_PoolBody_'
          type: array
          title: Actions
      additionalProperties: false
      type: object
      required:
      - actions
      title: BulkBody[PoolBody]
    BulkBody_VariableBody_:
      properties:
        actions:
          items:
            oneOf:
            - $ref: '#/components/schemas/BulkCreateAction_VariableBody_'
            - $ref: '#/components/schemas/BulkUpdateAction_VariableBody_'
            - $ref: '#/components/schemas/BulkDeleteAction_VariableBody_'
          type: array
          title: Actions
      additionalProperties: false
      type: object
      required:
      - actions
      title: BulkBody[VariableBody]
    BulkCreateAction_ConnectionBody_:
      properties:
        action:
          type: string
          const: create
          title: Action
          description: The action to be performed on the entities.
        entities:
          items:
            $ref: '#/components/schemas/ConnectionBody'
          type: array
          title: Entities
          description: A list of entities to be created.
        action_on_existence:
          $ref: '#/components/schemas/BulkActionOnExistence'
          default: fail
      additionalProperties: false
      type: object
      required:
      - action
      - entities
      title: BulkCreateAction[ConnectionBody]
    BulkCreateAction_PoolBody_:
      properties:
        action:
          type: string
          const: create
          title: Action
          description: The action to be performed on the entities.
        entities:
          items:
            $ref: '#/components/schemas/PoolBody'
          type: array
          title: Entities
          description: A list of entities to be created.
        action_on_existence:
          $ref: '#/components/schemas/BulkActionOnExistence'
          default: fail
      additionalProperties: false
      type: object
      required:
      - action
      - entities
      title: BulkCreateAction[PoolBody]
    BulkCreateAction_VariableBody_:
      properties:
        action:
          type: string
          const: create
          title: Action
          description: The action to be performed on the entities.
        entities:
          items:
            $ref: '#/components/schemas/VariableBody'
          type: array
          title: Entities
          description: A list of entities to be created.
        action_on_existence:
          $ref: '#/components/schemas/BulkActionOnExistence'
          default: fail
      additionalProperties: false
      type: object
      required:
      - action
      - entities
      title: BulkCreateAction[VariableBody]
    BulkDeleteAction_ConnectionBody_:
      properties:
        action:
          type: string
          const: delete
          title: Action
          description: The action to be performed on the entities.
        entities:
          items:
            type: string
          type: array
          title: Entities
          description: A list of entity id/key to be deleted.
        action_on_non_existence:
          $ref: '#/components/schemas/BulkActionNotOnExistence'
          default: fail
      additionalProperties: false
      type: object
      required:
      - action
      - entities
      title: BulkDeleteAction[ConnectionBody]
    BulkDeleteAction_PoolBody_:
      properties:
        action:
          type: string
          const: delete
          title: Action
          description: The action to be performed on the entities.
        entities:
          items:
            type: string
          type: array
          title: Entities
          description: A list of entity id/key to be deleted.
        action_on_non_existence:
          $ref: '#/components/schemas/BulkActionNotOnExistence'
          default: fail
      additionalProperties: false
      type: object
      required:
      - action
      - entities
      title: BulkDeleteAction[PoolBody]
    BulkDeleteAction_VariableBody_:
      properties:
        action:
          type: string
          const: delete
          title: Action
          description: The action to be performed on the entities.
        entities:
          items:
            type: string
          type: array
          title: Entities
          description: A list of entity id/key to be deleted.
        action_on_non_existence:
          $ref: '#/components/schemas/BulkActionNotOnExistence'
          default: fail
      additionalProperties: false
      type: object
      required:
      - action
      - entities
      title: BulkDeleteAction[VariableBody]
    BulkResponse:
      properties:
        create:
          anyOf:
          - $ref: '#/components/schemas/BulkActionResponse'
          - type: 'null'
          description: Details of the bulk create operation, including successful
            keys and errors.
        update:
          anyOf:
          - $ref: '#/components/schemas/BulkActionResponse'
          - type: 'null'
          description: Details of the bulk update operation, including successful
            keys and errors.
        delete:
          anyOf:
          - $ref: '#/components/schemas/BulkActionResponse'
          - type: 'null'
          description: Details of the bulk delete operation, including successful
            keys and errors.
      type: object
      title: BulkResponse
      description: 'Serializer for responses to bulk entity operations.


        This represents the results of create, update, and delete actions performed
        on entity in bulk.

        Each action (if requested) is represented as a field containing details about
        successful keys and any encountered errors.

        Fields are populated in the response only if the respective action was part
        of the request, else are set None.'
    BulkUpdateAction_ConnectionBody_:
      properties:
        action:
          type: string
          const: update
          title: Action
          description: The action to be performed on the entities.
        entities:
          items:
            $ref: '#/components/schemas/ConnectionBody'
          type: array
          title: Entities
          description: A list of entities to be updated.
        action_on_non_existence:
          $ref: '#/components/schemas/BulkActionNotOnExistence'
          default: fail
      additionalProperties: false
      type: object
      required:
      - action
      - entities
      title: BulkUpdateAction[ConnectionBody]
    BulkUpdateAction_PoolBody_:
      properties:
        action:
          type: string
          const: update
          title: Action
          description: The action to be performed on the entities.
        entities:
          items:
            $ref: '#/components/schemas/PoolBody'
          type: array
          title: Entities
          description: A list of entities to be updated.
        action_on_non_existence:
          $ref: '#/components/schemas/BulkActionNotOnExistence'
          default: fail
      additionalProperties: false
      type: object
      required:
      - action
      - entities
      title: BulkUpdateAction[PoolBody]
    BulkUpdateAction_VariableBody_:
      properties:
        action:
          type: string
          const: update
          title: Action
          description: The action to be performed on the entities.
        entities:
          items:
            $ref: '#/components/schemas/VariableBody'
          type: array
          title: Entities
          description: A list of entities to be updated.
        action_on_non_existence:
          $ref: '#/components/schemas/BulkActionNotOnExistence'
          default: fail
      additionalProperties: false
      type: object
      required:
      - action
      - entities
      title: BulkUpdateAction[VariableBody]
    ClearTaskInstancesBody:
      properties:
        dry_run:
          type: boolean
          title: Dry Run
          default: true
        start_date:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Start Date
        end_date:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: End Date
        only_failed:
          type: boolean
          title: Only Failed
          default: true
        only_running:
          type: boolean
          title: Only Running
          default: false
        reset_dag_runs:
          type: boolean
          title: Reset Dag Runs
          default: true
        task_ids:
          anyOf:
          - items:
              anyOf:
              - type: string
              - prefixItems:
                - type: string
                - type: integer
                type: array
                maxItems: 2
                minItems: 2
            type: array
          - type: 'null'
          title: Task Ids
        dag_run_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Dag Run Id
        include_upstream:
          type: boolean
          title: Include Upstream
          default: false
        include_downstream:
          type: boolean
          title: Include Downstream
          default: false
        include_future:
          type: boolean
          title: Include Future
          default: false
        include_past:
          type: boolean
          title: Include Past
          default: false
      additionalProperties: false
      type: object
      title: ClearTaskInstancesBody
      description: Request body for Clear Task Instances endpoint.
    Config:
      properties:
        sections:
          items:
            $ref: '#/components/schemas/ConfigSection'
          type: array
          title: Sections
      additionalProperties: false
      type: object
      required:
      - sections
      title: Config
      description: List of config sections with their options.
    ConfigOption:
      properties:
        key:
          type: string
          title: Key
        value:
          anyOf:
          - type: string
          - prefixItems:
            - type: string
            - type: string
            type: array
            maxItems: 2
            minItems: 2
          title: Value
      additionalProperties: false
      type: object
      required:
      - key
      - value
      title: ConfigOption
      description: Config option.
    ConfigSection:
      properties:
        name:
          type: string
          title: Name
        options:
          items:
            $ref: '#/components/schemas/ConfigOption'
          type: array
          title: Options
      additionalProperties: false
      type: object
      required:
      - name
      - options
      title: ConfigSection
      description: Config Section Schema.
    ConnectionBody:
      properties:
        connection_id:
          type: string
          maxLength: 200
          pattern: ^[\w.-]+$
          title: Connection Id
        conn_type:
          type: string
          title: Conn Type
        description:
          anyOf:
          - type: string
          - type: 'null'
          title: Description
        host:
          anyOf:
          - type: string
          - type: 'null'
          title: Host
        login:
          anyOf:
          - type: string
          - type: 'null'
          title: Login
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Schema
        port:
          anyOf:
          - type: integer
          - type: 'null'
          title: Port
        password:
          anyOf:
          - type: string
          - type: 'null'
          title: Password
        extra:
          anyOf:
          - type: string
          - type: 'null'
          title: Extra
      additionalProperties: false
      type: object
      required:
      - connection_id
      - conn_type
      title: ConnectionBody
      description: Connection Serializer for requests body.
    ConnectionCollectionResponse:
      properties:
        connections:
          items:
            $ref: '#/components/schemas/ConnectionResponse'
          type: array
          title: Connections
        total_entries:
          type: integer
          title: Total Entries
      type: object
      required:
      - connections
      - total_entries
      title: ConnectionCollectionResponse
      description: Connection Collection serializer for responses.
    ConnectionResponse:
      properties:
        connection_id:
          type: string
          title: Connection Id
        conn_type:
          type: string
          title: Conn Type
        description:
          anyOf:
          - type: string
          - type: 'null'
          title: Description
        host:
          anyOf:
          - type: string
          - type: 'null'
          title: Host
        login:
          anyOf:
          - type: string
          - type: 'null'
          title: Login
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Schema
        port:
          anyOf:
          - type: integer
          - type: 'null'
          title: Port
        password:
          anyOf:
          - type: string
          - type: 'null'
          title: Password
        extra:
          anyOf:
          - type: string
          - type: 'null'
          title: Extra
      type: object
      required:
      - connection_id
      - conn_type
      - description
      - host
      - login
      - schema
      - port
      - password
      - extra
      title: ConnectionResponse
      description: Connection serializer for responses.
    ConnectionTestResponse:
      properties:
        status:
          type: boolean
          title: Status
        message:
          type: string
          title: Message
      type: object
      required:
      - status
      - message
      title: ConnectionTestResponse
      description: Connection Test serializer for responses.
    CreateAssetEventsBody:
      properties:
        asset_id:
          type: integer
          title: Asset Id
        extra:
          additionalProperties: true
          type: object
          title: Extra
      additionalProperties: false
      type: object
      required:
      - asset_id
      title: CreateAssetEventsBody
      description: Create asset events request.
    DAGCollectionResponse:
      properties:
        dags:
          items:
            $ref: '#/components/schemas/DAGResponse'
          type: array
          title: Dags
        total_entries:
          type: integer
          title: Total Entries
      type: object
      required:
      - dags
      - total_entries
      title: DAGCollectionResponse
      description: DAG Collection serializer for responses.
    DAGDetailsResponse:
      properties:
        dag_id:
          type: string
          title: Dag Id
        dag_display_name:
          type: string
          title: Dag Display Name
        is_paused:
          type: boolean
          title: Is Paused
        is_stale:
          type: boolean
          title: Is Stale
        last_parsed_time:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Last Parsed Time
        last_expired:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Last Expired
        bundle_name:
          anyOf:
          - type: string
          - type: 'null'
          title: Bundle Name
        bundle_version:
          anyOf:
          - type: string
          - type: 'null'
          title: Bundle Version
        relative_fileloc:
          anyOf:
          - type: string
          - type: 'null'
          title: Relative Fileloc
        fileloc:
          type: string
          title: Fileloc
        description:
          anyOf:
          - type: string
          - type: 'null'
          title: Description
        timetable_summary:
          anyOf:
          - type: string
          - type: 'null'
          title: Timetable Summary
        timetable_description:
          anyOf:
          - type: string
          - type: 'null'
          title: Timetable Description
        tags:
          items:
            $ref: '#/components/schemas/DagTagResponse'
          type: array
          title: Tags
        max_active_tasks:
          type: integer
          title: Max Active Tasks
        max_active_runs:
          anyOf:
          - type: integer
          - type: 'null'
          title: Max Active Runs
        max_consecutive_failed_dag_runs:
          type: integer
          title: Max Consecutive Failed Dag Runs
        has_task_concurrency_limits:
          type: boolean
          title: Has Task Concurrency Limits
        has_import_errors:
          type: boolean
          title: Has Import Errors
        next_dagrun_logical_date:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Next Dagrun Logical Date
        next_dagrun_data_interval_start:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Next Dagrun Data Interval Start
        next_dagrun_data_interval_end:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Next Dagrun Data Interval End
        next_dagrun_run_after:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Next Dagrun Run After
        owners:
          items:
            type: string
          type: array
          title: Owners
        catchup:
          type: boolean
          title: Catchup
        dag_run_timeout:
          anyOf:
          - type: string
            format: duration
          - type: 'null'
          title: Dag Run Timeout
        asset_expression:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Asset Expression
        doc_md:
          anyOf:
          - type: string
          - type: 'null'
          title: Doc Md
        start_date:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Start Date
        end_date:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: End Date
        is_paused_upon_creation:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Is Paused Upon Creation
        params:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Params
        render_template_as_native_obj:
          type: boolean
          title: Render Template As Native Obj
        template_search_path:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Template Search Path
        timezone:
          anyOf:
          - type: string
          - type: 'null'
          title: Timezone
        last_parsed:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Last Parsed
        owner_links:
          anyOf:
          - additionalProperties:
              type: string
            type: object
          - type: 'null'
          title: Owner Links
        file_token:
          type: string
          title: File Token
          description: Return file token.
          readOnly: true
        concurrency:
          type: integer
          title: Concurrency
          description: Return max_active_tasks as concurrency.
          readOnly: true
        latest_dag_version:
          anyOf:
          - $ref: '#/components/schemas/DagVersionResponse'
          - type: 'null'
          description: Return the latest DagVersion.
          readOnly: true
      type: object
      required:
      - dag_id
      - dag_display_name
      - is_paused
      - is_stale
      - last_parsed_time
      - last_expired
      - bundle_name
      - bundle_version
      - relative_fileloc
      - fileloc
      - description
      - timetable_summary
      - timetable_description
      - tags
      - max_active_tasks
      - max_active_runs
      - max_consecutive_failed_dag_runs
      - has_task_concurrency_limits
      - has_import_errors
      - next_dagrun_logical_date
      - next_dagrun_data_interval_start
      - next_dagrun_data_interval_end
      - next_dagrun_run_after
      - owners
      - catchup
      - dag_run_timeout
      - asset_expression
      - doc_md
      - start_date
      - end_date
      - is_paused_upon_creation
      - params
      - render_template_as_native_obj
      - template_search_path
      - timezone
      - last_parsed
      - file_token
      - concurrency
      - latest_dag_version
      title: DAGDetailsResponse
      description: Specific serializer for DAG Details responses.
    DAGPatchBody:
      properties:
        is_paused:
          type: boolean
          title: Is Paused
      additionalProperties: false
      type: object
      required:
      - is_paused
      title: DAGPatchBody
      description: Dag Serializer for updatable bodies.
    DAGResponse:
      properties:
        dag_id:
          type: string
          title: Dag Id
        dag_display_name:
          type: string
          title: Dag Display Name
        is_paused:
          type: boolean
          title: Is Paused
        is_stale:
          type: boolean
          title: Is Stale
        last_parsed_time:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Last Parsed Time
        last_expired:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Last Expired
        bundle_name:
          anyOf:
          - type: string
          - type: 'null'
          title: Bundle Name
        bundle_version:
          anyOf:
          - type: string
          - type: 'null'
          title: Bundle Version
        relative_fileloc:
          anyOf:
          - type: string
          - type: 'null'
          title: Relative Fileloc
        fileloc:
          type: string
          title: Fileloc
        description:
          anyOf:
          - type: string
          - type: 'null'
          title: Description
        timetable_summary:
          anyOf:
          - type: string
          - type: 'null'
          title: Timetable Summary
        timetable_description:
          anyOf:
          - type: string
          - type: 'null'
          title: Timetable Description
        tags:
          items:
            $ref: '#/components/schemas/DagTagResponse'
          type: array
          title: Tags
        max_active_tasks:
          type: integer
          title: Max Active Tasks
        max_active_runs:
          anyOf:
          - type: integer
          - type: 'null'
          title: Max Active Runs
        max_consecutive_failed_dag_runs:
          type: integer
          title: Max Consecutive Failed Dag Runs
        has_task_concurrency_limits:
          type: boolean
          title: Has Task Concurrency Limits
        has_import_errors:
          type: boolean
          title: Has Import Errors
        next_dagrun_logical_date:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Next Dagrun Logical Date
        next_dagrun_data_interval_start:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Next Dagrun Data Interval Start
        next_dagrun_data_interval_end:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Next Dagrun Data Interval End
        next_dagrun_run_after:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Next Dagrun Run After
        owners:
          items:
            type: string
          type: array
          title: Owners
        file_token:
          type: string
          title: File Token
          description: Return file token.
          readOnly: true
      type: object
      required:
      - dag_id
      - dag_display_name
      - is_paused
      - is_stale
      - last_parsed_time
      - last_expired
      - bundle_name
      - bundle_version
      - relative_fileloc
      - fileloc
      - description
      - timetable_summary
      - timetable_description
      - tags
      - max_active_tasks
      - max_active_runs
      - max_consecutive_failed_dag_runs
      - has_task_concurrency_limits
      - has_import_errors
      - next_dagrun_logical_date
      - next_dagrun_data_interval_start
      - next_dagrun_data_interval_end
      - next_dagrun_run_after
      - owners
      - file_token
      title: DAGResponse
      description: DAG serializer for responses.
    DAGRunClearBody:
      properties:
        dry_run:
          type: boolean
          title: Dry Run
          default: true
        only_failed:
          type: boolean
          title: Only Failed
          default: false
      additionalProperties: false
      type: object
      title: DAGRunClearBody
      description: DAG Run serializer for clear endpoint body.
    DAGRunCollectionResponse:
      properties:
        dag_runs:
          items:
            $ref: '#/components/schemas/DAGRunResponse'
          type: array
          title: Dag Runs
        total_entries:
          type: integer
          title: Total Entries
      type: object
      required:
      - dag_runs
      - total_entries
      title: DAGRunCollectionResponse
      description: DAG Run Collection serializer for responses.
    DAGRunPatchBody:
      properties:
        state:
          anyOf:
          - $ref: '#/components/schemas/DAGRunPatchStates'
          - type: 'null'
        note:
          anyOf:
          - type: string
            maxLength: 1000
          - type: 'null'
          title: Note
      additionalProperties: false
      type: object
      title: DAGRunPatchBody
      description: DAG Run Serializer for PATCH requests.
    DAGRunPatchStates:
      type: string
      enum:
      - queued
      - success
      - failed
      title: DAGRunPatchStates
      description: Enum for DAG Run states when updating a DAG Run.
    DAGRunResponse:
      properties:
        dag_run_id:
          type: string
          title: Dag Run Id
        dag_id:
          type: string
          title: Dag Id
        logical_date:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Logical Date
        queued_at:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Queued At
        start_date:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Start Date
        end_date:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: End Date
        data_interval_start:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Data Interval Start
        data_interval_end:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Data Interval End
        run_after:
          type: string
          format: date-time
          title: Run After
        last_scheduling_decision:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Last Scheduling Decision
        run_type:
          $ref: '#/components/schemas/DagRunType'
        state:
          $ref: '#/components/schemas/DagRunState'
        triggered_by:
          anyOf:
          - $ref: '#/components/schemas/DagRunTriggeredByType'
          - type: 'null'
        conf:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Conf
        note:
          anyOf:
          - type: string
          - type: 'null'
          title: Note
        dag_versions:
          items:
            $ref: '#/components/schemas/DagVersionResponse'
          type: array
          title: Dag Versions
        bundle_version:
          anyOf:
          - type: string
          - type: 'null'
          title: Bundle Version
      type: object
      required:
      - dag_run_id
      - dag_id
      - logical_date
      - queued_at
      - start_date
      - end_date
      - data_interval_start
      - data_interval_end
      - run_after
      - last_scheduling_decision
      - run_type
      - state
      - triggered_by
      - conf
      - note
      - dag_versions
      - bundle_version
      title: DAGRunResponse
      description: DAG Run serializer for responses.
    DAGRunsBatchBody:
      properties:
        order_by:
          anyOf:
          - type: string
          - type: 'null'
          title: Order By
        page_offset:
          type: integer
          minimum: 0.0
          title: Page Offset
          default: 0
        page_limit:
          type: integer
          minimum: 0.0
          title: Page Limit
          default: 100
        dag_ids:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Dag Ids
        states:
          anyOf:
          - items:
              anyOf:
              - $ref: '#/components/schemas/DagRunState'
              - type: 'null'
            type: array
          - type: 'null'
          title: States
        run_after_gte:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Run After Gte
        run_after_lte:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Run After Lte
        logical_date_gte:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Logical Date Gte
        logical_date_lte:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Logical Date Lte
        start_date_gte:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Start Date Gte
        start_date_lte:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Start Date Lte
        end_date_gte:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: End Date Gte
        end_date_lte:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: End Date Lte
      additionalProperties: false
      type: object
      title: DAGRunsBatchBody
      description: List DAG Runs body for batch endpoint.
    DAGSourceResponse:
      properties:
        content:
          anyOf:
          - type: string
          - type: 'null'
          title: Content
        dag_id:
          type: string
          title: Dag Id
        version_number:
          anyOf:
          - type: integer
          - type: 'null'
          title: Version Number
      type: object
      required:
      - content
      - dag_id
      - version_number
      title: DAGSourceResponse
      description: DAG Source serializer for responses.
    DAGTagCollectionResponse:
      properties:
        tags:
          items:
            type: string
          type: array
          title: Tags
        total_entries:
          type: integer
          title: Total Entries
      type: object
      required:
      - tags
      - total_entries
      title: DAGTagCollectionResponse
      description: DAG Tags Collection serializer for responses.
    DAGVersionCollectionResponse:
      properties:
        dag_versions:
          items:
            $ref: '#/components/schemas/DagVersionResponse'
          type: array
          title: Dag Versions
        total_entries:
          type: integer
          title: Total Entries
      type: object
      required:
      - dag_versions
      - total_entries
      title: DAGVersionCollectionResponse
      description: DAG Version Collection serializer for responses.
    DAGWarningCollectionResponse:
      properties:
        dag_warnings:
          items:
            $ref: '#/components/schemas/DAGWarningResponse'
          type: array
          title: Dag Warnings
        total_entries:
          type: integer
          title: Total Entries
      type: object
      required:
      - dag_warnings
      - total_entries
      title: DAGWarningCollectionResponse
      description: DAG warning collection serializer for responses.
    DAGWarningResponse:
      properties:
        dag_id:
          type: string
          title: Dag Id
        warning_type:
          $ref: '#/components/schemas/DagWarningType'
        message:
          type: string
          title: Message
        timestamp:
          type: string
          format: date-time
          title: Timestamp
      type: object
      required:
      - dag_id
      - warning_type
      - message
      - timestamp
      title: DAGWarningResponse
      description: DAG Warning serializer for responses.
    DagProcessorInfoResponse:
      properties:
        status:
          anyOf:
          - type: string
          - type: 'null'
          title: Status
        latest_dag_processor_heartbeat:
          anyOf:
          - type: string
          - type: 'null'
          title: Latest Dag Processor Heartbeat
      type: object
      required:
      - status
      - latest_dag_processor_heartbeat
      title: DagProcessorInfoResponse
      description: DagProcessor info serializer for responses.
    DagRunAssetReference:
      properties:
        run_id:
          type: string
          title: Run Id
        dag_id:
          type: string
          title: Dag Id
        logical_date:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Logical Date
        start_date:
          type: string
          format: date-time
          title: Start Date
        end_date:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: End Date
        state:
          type: string
          title: State
        data_interval_start:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Data Interval Start
        data_interval_end:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Data Interval End
      additionalProperties: false
      type: object
      required:
      - run_id
      - dag_id
      - logical_date
      - start_date
      - end_date
      - state
      - data_interval_start
      - data_interval_end
      title: DagRunAssetReference
      description: DAGRun serializer for asset responses.
    DagRunState:
      type: string
      enum:
      - queued
      - running
      - success
      - failed
      title: DagRunState
      description: 'All possible states that a DagRun can be in.


        These are "shared" with TaskInstanceState in some parts of the code,

        so please ensure that their values always match the ones with the

        same name in TaskInstanceState.'
    DagRunTriggeredByType:
      type: string
      enum:
      - cli
      - operator
      - rest_api
      - ui
      - test
      - timetable
      - asset
      - backfill
      title: DagRunTriggeredByType
      description: Class with TriggeredBy types for DagRun.
    DagRunType:
      type: string
      enum:
      - backfill
      - scheduled
      - manual
      - asset_triggered
      title: DagRunType
      description: Class with DagRun types.
    DagScheduleAssetReference:
      properties:
        dag_id:
          type: string
          title: Dag Id
        created_at:
          type: string
          format: date-time
          title: Created At
        updated_at:
          type: string
          format: date-time
          title: Updated At
      additionalProperties: false
      type: object
      required:
      - dag_id
      - created_at
      - updated_at
      title: DagScheduleAssetReference
      description: DAG schedule reference serializer for assets.
    DagStatsCollectionResponse:
      properties:
        dags:
          items:
            $ref: '#/components/schemas/DagStatsResponse'
          type: array
          title: Dags
        total_entries:
          type: integer
          title: Total Entries
      type: object
      required:
      - dags
      - total_entries
      title: DagStatsCollectionResponse
      description: DAG Stats Collection serializer for responses.
    DagStatsResponse:
      properties:
        dag_id:
          type: string
          title: Dag Id
        stats:
          items:
            $ref: '#/components/schemas/DagStatsStateResponse'
          type: array
          title: Stats
      type: object
      required:
      - dag_id
      - stats
      title: DagStatsResponse
      description: DAG Stats serializer for responses.
    DagStatsStateResponse:
      properties:
        state:
          $ref: '#/components/schemas/DagRunState'
        count:
          type: integer
          title: Count
      type: object
      required:
      - state
      - count
      title: DagStatsStateResponse
      description: DagStatsState serializer for responses.
    DagTagResponse:
      properties:
        name:
          type: string
          title: Name
        dag_id:
          type: string
          title: Dag Id
      type: object
      required:
      - name
      - dag_id
      title: DagTagResponse
      description: DAG Tag serializer for responses.
    DagVersionResponse:
      properties:
        id:
          type: string
          format: uuid
          title: Id
        version_number:
          type: integer
          title: Version Number
        dag_id:
          type: string
          title: Dag Id
        bundle_name:
          anyOf:
          - type: string
          - type: 'null'
          title: Bundle Name
        bundle_version:
          anyOf:
          - type: string
          - type: 'null'
          title: Bundle Version
        created_at:
          type: string
          format: date-time
          title: Created At
        bundle_url:
          anyOf:
          - type: string
          - type: 'null'
          title: Bundle Url
          readOnly: true
      type: object
      required:
      - id
      - version_number
      - dag_id
      - bundle_name
      - bundle_version
      - created_at
      - bundle_url
      title: DagVersionResponse
      description: Dag Version serializer for responses.
    DagWarningType:
      type: string
      enum:
      - asset conflict
      - non-existent pool
      title: DagWarningType
      description: 'Enum for DAG warning types.


        This is the set of allowable values for the ``warning_type`` field

        in the DagWarning model.'
    DryRunBackfillCollectionResponse:
      properties:
        backfills:
          items:
            $ref: '#/components/schemas/DryRunBackfillResponse'
          type: array
          title: Backfills
        total_entries:
          type: integer
          title: Total Entries
      type: object
      required:
      - backfills
      - total_entries
      title: DryRunBackfillCollectionResponse
      description: Backfill collection serializer for responses in dry-run mode.
    DryRunBackfillResponse:
      properties:
        logical_date:
          type: string
          format: date-time
          title: Logical Date
      type: object
      required:
      - logical_date
      title: DryRunBackfillResponse
      description: Backfill serializer for responses in dry-run mode.
    EventLogCollectionResponse:
      properties:
        event_logs:
          items:
            $ref: '#/components/schemas/EventLogResponse'
          type: array
          title: Event Logs
        total_entries:
          type: integer
          title: Total Entries
      type: object
      required:
      - event_logs
      - total_entries
      title: EventLogCollectionResponse
      description: Event Log Collection Response.
    EventLogResponse:
      properties:
        event_log_id:
          type: integer
          title: Event Log Id
        when:
          type: string
          format: date-time
          title: When
        dag_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Dag Id
        task_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Task Id
        run_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Run Id
        map_index:
          anyOf:
          - type: integer
          - type: 'null'
          title: Map Index
        try_number:
          anyOf:
          - type: integer
          - type: 'null'
          title: Try Number
        event:
          type: string
          title: Event
        logical_date:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Logical Date
        owner:
          anyOf:
          - type: string
          - type: 'null'
          title: Owner
        extra:
          anyOf:
          - type: string
          - type: 'null'
          title: Extra
      type: object
      required:
      - event_log_id
      - when
      - dag_id
      - task_id
      - run_id
      - map_index
      - try_number
      - event
      - logical_date
      - owner
      - extra
      title: EventLogResponse
      description: Event Log Response.
    ExtraLinkCollectionResponse:
      properties:
        extra_links:
          additionalProperties:
            anyOf:
            - type: string
            - type: 'null'
          type: object
          title: Extra Links
        total_entries:
          type: integer
          title: Total Entries
      type: object
      required:
      - extra_links
      - total_entries
      title: ExtraLinkCollectionResponse
      description: Extra Links Response.
    FastAPIAppResponse:
      properties:
        app:
          type: string
          title: App
        url_prefix:
          type: string
          title: Url Prefix
        name:
          type: string
          title: Name
      additionalProperties: true
      type: object
      required:
      - app
      - url_prefix
      - name
      title: FastAPIAppResponse
      description: Serializer for Plugin FastAPI App responses.
    FastAPIRootMiddlewareResponse:
      properties:
        middleware:
          type: string
          title: Middleware
        name:
          type: string
          title: Name
      additionalProperties: true
      type: object
      required:
      - middleware
      - name
      title: FastAPIRootMiddlewareResponse
      description: Serializer for Plugin FastAPI root middleware responses.
    HTTPExceptionResponse:
      properties:
        detail:
          anyOf:
          - type: string
          - additionalProperties: true
            type: object
          title: Detail
      type: object
      required:
      - detail
      title: HTTPExceptionResponse
      description: HTTPException Model used for error response.
    HTTPValidationError:
      properties:
        detail:
          items:
            $ref: '#/components/schemas/ValidationError'
          type: array
          title: Detail
      type: object
      title: HTTPValidationError
    HealthInfoResponse:
      properties:
        metadatabase:
          $ref: '#/components/schemas/BaseInfoResponse'
        scheduler:
          $ref: '#/components/schemas/SchedulerInfoResponse'
        triggerer:
          $ref: '#/components/schemas/TriggererInfoResponse'
        dag_processor:
          anyOf:
          - $ref: '#/components/schemas/DagProcessorInfoResponse'
          - type: 'null'
      type: object
      required:
      - metadatabase
      - scheduler
      - triggerer
      title: HealthInfoResponse
      description: Health serializer for responses.
    ImportErrorCollectionResponse:
      properties:
        import_errors:
          items:
            $ref: '#/components/schemas/ImportErrorResponse'
          type: array
          title: Import Errors
        total_entries:
          type: integer
          title: Total Entries
      type: object
      required:
      - import_errors
      - total_entries
      title: ImportErrorCollectionResponse
      description: Import Error Collection Response.
    ImportErrorResponse:
      properties:
        import_error_id:
          type: integer
          title: Import Error Id
        timestamp:
          type: string
          format: date-time
          title: Timestamp
        filename:
          type: string
          title: Filename
        bundle_name:
          anyOf:
          - type: string
          - type: 'null'
          title: Bundle Name
        stack_trace:
          type: string
          title: Stack Trace
      type: object
      required:
      - import_error_id
      - timestamp
      - filename
      - bundle_name
      - stack_trace
      title: ImportErrorResponse
      description: Import Error Response.
    JobCollectionResponse:
      properties:
        jobs:
          items:
            $ref: '#/components/schemas/JobResponse'
          type: array
          title: Jobs
        total_entries:
          type: integer
          title: Total Entries
      type: object
      required:
      - jobs
      - total_entries
      title: JobCollectionResponse
      description: Job Collection Response.
    JobResponse:
      properties:
        id:
          type: integer
          title: Id
        dag_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Dag Id
        state:
          anyOf:
          - type: string
          - type: 'null'
          title: State
        job_type:
          anyOf:
          - type: string
          - type: 'null'
          title: Job Type
        start_date:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Start Date
        end_date:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: End Date
        latest_heartbeat:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Latest Heartbeat
        executor_class:
          anyOf:
          - type: string
          - type: 'null'
          title: Executor Class
        hostname:
          anyOf:
          - type: string
          - type: 'null'
          title: Hostname
        unixname:
          anyOf:
          - type: string
          - type: 'null'
          title: Unixname
      type: object
      required:
      - id
      - dag_id
      - state
      - job_type
      - start_date
      - end_date
      - latest_heartbeat
      - executor_class
      - hostname
      - unixname
      title: JobResponse
      description: Job serializer for responses.
    JsonValue: {}
    PatchTaskInstanceBody:
      properties:
        new_state:
          anyOf:
          - $ref: '#/components/schemas/TaskInstanceState'
          - type: 'null'
        note:
          anyOf:
          - type: string
            maxLength: 1000
          - type: 'null'
          title: Note
        include_upstream:
          type: boolean
          title: Include Upstream
          default: false
        include_downstream:
          type: boolean
          title: Include Downstream
          default: false
        include_future:
          type: boolean
          title: Include Future
          default: false
        include_past:
          type: boolean
          title: Include Past
          default: false
      additionalProperties: false
      type: object
      title: PatchTaskInstanceBody
      description: Request body for Clear Task Instances endpoint.
    PluginCollectionResponse:
      properties:
        plugins:
          items:
            $ref: '#/components/schemas/PluginResponse'
          type: array
          title: Plugins
        total_entries:
          type: integer
          title: Total Entries
      type: object
      required:
      - plugins
      - total_entries
      title: PluginCollectionResponse
      description: Plugin Collection serializer.
    PluginResponse:
      properties:
        name:
          type: string
          title: Name
        macros:
          items:
            type: string
          type: array
          title: Macros
        flask_blueprints:
          items:
            type: string
          type: array
          title: Flask Blueprints
        fastapi_apps:
          items:
            $ref: '#/components/schemas/FastAPIAppResponse'
          type: array
          title: Fastapi Apps
        fastapi_root_middlewares:
          items:
            $ref: '#/components/schemas/FastAPIRootMiddlewareResponse'
          type: array
          title: Fastapi Root Middlewares
        appbuilder_views:
          items:
            $ref: '#/components/schemas/AppBuilderViewResponse'
          type: array
          title: Appbuilder Views
        appbuilder_menu_items:
          items:
            $ref: '#/components/schemas/AppBuilderMenuItemResponse'
          type: array
          title: Appbuilder Menu Items
        global_operator_extra_links:
          items:
            type: string
          type: array
          title: Global Operator Extra Links
        operator_extra_links:
          items:
            type: string
          type: array
          title: Operator Extra Links
        source:
          type: string
          title: Source
        listeners:
          items:
            type: string
          type: array
          title: Listeners
        timetables:
          items:
            type: string
          type: array
          title: Timetables
      type: object
      required:
      - name
      - macros
      - flask_blueprints
      - fastapi_apps
      - fastapi_root_middlewares
      - appbuilder_views
      - appbuilder_menu_items
      - global_operator_extra_links
      - operator_extra_links
      - source
      - listeners
      - timetables
      title: PluginResponse
      description: Plugin serializer.
    PoolBody:
      properties:
        name:
          type: string
          maxLength: 256
          title: Name
        slots:
          type: integer
          title: Slots
        description:
          anyOf:
          - type: string
          - type: 'null'
          title: Description
        include_deferred:
          type: boolean
          title: Include Deferred
          default: false
      additionalProperties: false
      type: object
      required:
      - name
      - slots
      title: PoolBody
      description: Pool serializer for post bodies.
    PoolCollectionResponse:
      properties:
        pools:
          items:
            $ref: '#/components/schemas/PoolResponse'
          type: array
          title: Pools
        total_entries:
          type: integer
          title: Total Entries
      type: object
      required:
      - pools
      - total_entries
      title: PoolCollectionResponse
      description: Pool Collection serializer for responses.
    PoolPatchBody:
      properties:
        pool:
          anyOf:
          - type: string
          - type: 'null'
          title: Pool
        slots:
          anyOf:
          - type: integer
          - type: 'null'
          title: Slots
        description:
          anyOf:
          - type: string
          - type: 'null'
          title: Description
        include_deferred:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Include Deferred
      additionalProperties: false
      type: object
      title: PoolPatchBody
      description: Pool serializer for patch bodies.
    PoolResponse:
      properties:
        name:
          type: string
          title: Name
        slots:
          type: integer
          title: Slots
        description:
          anyOf:
          - type: string
          - type: 'null'
          title: Description
        include_deferred:
          type: boolean
          title: Include Deferred
        occupied_slots:
          type: integer
          title: Occupied Slots
        running_slots:
          type: integer
          title: Running Slots
        queued_slots:
          type: integer
          title: Queued Slots
        scheduled_slots:
          type: integer
          title: Scheduled Slots
        open_slots:
          type: integer
          title: Open Slots
        deferred_slots:
          type: integer
          title: Deferred Slots
      type: object
      required:
      - name
      - slots
      - description
      - include_deferred
      - occupied_slots
      - running_slots
      - queued_slots
      - scheduled_slots
      - open_slots
      - deferred_slots
      title: PoolResponse
      description: Pool serializer for responses.
    ProviderCollectionResponse:
      properties:
        providers:
          items:
            $ref: '#/components/schemas/ProviderResponse'
          type: array
          title: Providers
        total_entries:
          type: integer
          title: Total Entries
      type: object
      required:
      - providers
      - total_entries
      title: ProviderCollectionResponse
      description: Provider Collection serializer for responses.
    ProviderResponse:
      properties:
        package_name:
          type: string
          title: Package Name
        description:
          type: string
          title: Description
        version:
          type: string
          title: Version
      type: object
      required:
      - package_name
      - description
      - version
      title: ProviderResponse
      description: Provider serializer for responses.
    QueuedEventCollectionResponse:
      properties:
        queued_events:
          items:
            $ref: '#/components/schemas/QueuedEventResponse'
          type: array
          title: Queued Events
        total_entries:
          type: integer
          title: Total Entries
      type: object
      required:
      - queued_events
      - total_entries
      title: QueuedEventCollectionResponse
      description: Queued Event Collection serializer for responses.
    QueuedEventResponse:
      properties:
        dag_id:
          type: string
          title: Dag Id
        asset_id:
          type: integer
          title: Asset Id
        created_at:
          type: string
          format: date-time
          title: Created At
      type: object
      required:
      - dag_id
      - asset_id
      - created_at
      title: QueuedEventResponse
      description: Queued Event serializer for responses..
    ReprocessBehavior:
      type: string
      enum:
      - failed
      - completed
      - none
      title: ReprocessBehavior
      description: 'Internal enum for setting reprocess behavior in a backfill.


        :meta private:'
    SchedulerInfoResponse:
      properties:
        status:
          anyOf:
          - type: string
          - type: 'null'
          title: Status
        latest_scheduler_heartbeat:
          anyOf:
          - type: string
          - type: 'null'
          title: Latest Scheduler Heartbeat
      type: object
      required:
      - status
      - latest_scheduler_heartbeat
      title: SchedulerInfoResponse
      description: Scheduler info serializer for responses.
    StructuredLogMessage:
      properties:
        timestamp:
          type: string
          format: date-time
          title: Timestamp
        event:
          type: string
          title: Event
      additionalProperties: true
      type: object
      required:
      - event
      title: StructuredLogMessage
      description: An individual log message.
    TaskCollectionResponse:
      properties:
        tasks:
          items:
            $ref: '#/components/schemas/TaskResponse'
          type: array
          title: Tasks
        total_entries:
          type: integer
          title: Total Entries
      type: object
      required:
      - tasks
      - total_entries
      title: TaskCollectionResponse
      description: Task collection serializer for responses.
    TaskDependencyCollectionResponse:
      properties:
        dependencies:
          items:
            $ref: '#/components/schemas/TaskDependencyResponse'
          type: array
          title: Dependencies
      type: object
      required:
      - dependencies
      title: TaskDependencyCollectionResponse
      description: Task scheduling dependencies collection serializer for responses.
    TaskDependencyResponse:
      properties:
        name:
          type: string
          title: Name
        reason:
          type: string
          title: Reason
      type: object
      required:
      - name
      - reason
      title: TaskDependencyResponse
      description: Task Dependency serializer for responses.
    TaskInstanceCollectionResponse:
      properties:
        task_instances:
          items:
            $ref: '#/components/schemas/TaskInstanceResponse'
          type: array
          title: Task Instances
        total_entries:
          type: integer
          title: Total Entries
      type: object
      required:
      - task_instances
      - total_entries
      title: TaskInstanceCollectionResponse
      description: Task Instance Collection serializer for responses.
    TaskInstanceHistoryCollectionResponse:
      properties:
        task_instances:
          items:
            $ref: '#/components/schemas/TaskInstanceHistoryResponse'
          type: array
          title: Task Instances
        total_entries:
          type: integer
          title: Total Entries
      type: object
      required:
      - task_instances
      - total_entries
      title: TaskInstanceHistoryCollectionResponse
      description: TaskInstanceHistory Collection serializer for responses.
    TaskInstanceHistoryResponse:
      properties:
        task_id:
          type: string
          title: Task Id
        dag_id:
          type: string
          title: Dag Id
        dag_run_id:
          type: string
          title: Dag Run Id
        map_index:
          type: integer
          title: Map Index
        start_date:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Start Date
        end_date:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: End Date
        duration:
          anyOf:
          - type: number
          - type: 'null'
          title: Duration
        state:
          anyOf:
          - $ref: '#/components/schemas/TaskInstanceState'
          - type: 'null'
        try_number:
          type: integer
          title: Try Number
        max_tries:
          type: integer
          title: Max Tries
        task_display_name:
          type: string
          title: Task Display Name
        hostname:
          anyOf:
          - type: string
          - type: 'null'
          title: Hostname
        unixname:
          anyOf:
          - type: string
          - type: 'null'
          title: Unixname
        pool:
          type: string
          title: Pool
        pool_slots:
          type: integer
          title: Pool Slots
        queue:
          anyOf:
          - type: string
          - type: 'null'
          title: Queue
        priority_weight:
          anyOf:
          - type: integer
          - type: 'null'
          title: Priority Weight
        operator:
          anyOf:
          - type: string
          - type: 'null'
          title: Operator
        queued_when:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Queued When
        scheduled_when:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Scheduled When
        pid:
          anyOf:
          - type: integer
          - type: 'null'
          title: Pid
        executor:
          anyOf:
          - type: string
          - type: 'null'
          title: Executor
        executor_config:
          type: string
          title: Executor Config
        dag_version:
          anyOf:
          - $ref: '#/components/schemas/DagVersionResponse'
          - type: 'null'
      type: object
      required:
      - task_id
      - dag_id
      - dag_run_id
      - map_index
      - start_date
      - end_date
      - duration
      - state
      - try_number
      - max_tries
      - task_display_name
      - hostname
      - unixname
      - pool
      - pool_slots
      - queue
      - priority_weight
      - operator
      - queued_when
      - scheduled_when
      - pid
      - executor
      - executor_config
      - dag_version
      title: TaskInstanceHistoryResponse
      description: TaskInstanceHistory serializer for responses.
    TaskInstanceResponse:
      properties:
        id:
          type: string
          title: Id
        task_id:
          type: string
          title: Task Id
        dag_id:
          type: string
          title: Dag Id
        dag_run_id:
          type: string
          title: Dag Run Id
        map_index:
          type: integer
          title: Map Index
        logical_date:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Logical Date
        run_after:
          type: string
          format: date-time
          title: Run After
        start_date:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Start Date
        end_date:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: End Date
        duration:
          anyOf:
          - type: number
          - type: 'null'
          title: Duration
        state:
          anyOf:
          - $ref: '#/components/schemas/TaskInstanceState'
          - type: 'null'
        try_number:
          type: integer
          title: Try Number
        max_tries:
          type: integer
          title: Max Tries
        task_display_name:
          type: string
          title: Task Display Name
        hostname:
          anyOf:
          - type: string
          - type: 'null'
          title: Hostname
        unixname:
          anyOf:
          - type: string
          - type: 'null'
          title: Unixname
        pool:
          type: string
          title: Pool
        pool_slots:
          type: integer
          title: Pool Slots
        queue:
          anyOf:
          - type: string
          - type: 'null'
          title: Queue
        priority_weight:
          anyOf:
          - type: integer
          - type: 'null'
          title: Priority Weight
        operator:
          anyOf:
          - type: string
          - type: 'null'
          title: Operator
        queued_when:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Queued When
        scheduled_when:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Scheduled When
        pid:
          anyOf:
          - type: integer
          - type: 'null'
          title: Pid
        executor:
          anyOf:
          - type: string
          - type: 'null'
          title: Executor
        executor_config:
          type: string
          title: Executor Config
        note:
          anyOf:
          - type: string
          - type: 'null'
          title: Note
        rendered_map_index:
          anyOf:
          - type: string
          - type: 'null'
          title: Rendered Map Index
        rendered_fields:
          additionalProperties: true
          type: object
          title: Rendered Fields
        trigger:
          anyOf:
          - $ref: '#/components/schemas/TriggerResponse'
          - type: 'null'
        triggerer_job:
          anyOf:
          - $ref: '#/components/schemas/JobResponse'
          - type: 'null'
        dag_version:
          anyOf:
          - $ref: '#/components/schemas/DagVersionResponse'
          - type: 'null'
      type: object
      required:
      - id
      - task_id
      - dag_id
      - dag_run_id
      - map_index
      - logical_date
      - run_after
      - start_date
      - end_date
      - duration
      - state
      - try_number
      - max_tries
      - task_display_name
      - hostname
      - unixname
      - pool
      - pool_slots
      - queue
      - priority_weight
      - operator
      - queued_when
      - scheduled_when
      - pid
      - executor
      - executor_config
      - note
      - rendered_map_index
      - trigger
      - triggerer_job
      - dag_version
      title: TaskInstanceResponse
      description: TaskInstance serializer for responses.
    TaskInstanceState:
      type: string
      enum:
      - removed
      - scheduled
      - queued
      - running
      - success
      - restarting
      - failed
      - up_for_retry
      - up_for_reschedule
      - upstream_failed
      - skipped
      - deferred
      title: TaskInstanceState
      description: 'All possible states that a Task Instance can be in.


        Note that None is also allowed, so always use this in a type hint with Optional.'
    TaskInstancesBatchBody:
      properties:
        dag_ids:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Dag Ids
        dag_run_ids:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Dag Run Ids
        task_ids:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Task Ids
        state:
          anyOf:
          - items:
              anyOf:
              - $ref: '#/components/schemas/TaskInstanceState'
              - type: 'null'
            type: array
          - type: 'null'
          title: State
        run_after_gte:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Run After Gte
        run_after_lte:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Run After Lte
        logical_date_gte:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Logical Date Gte
        logical_date_lte:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Logical Date Lte
        start_date_gte:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Start Date Gte
        start_date_lte:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Start Date Lte
        end_date_gte:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: End Date Gte
        end_date_lte:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: End Date Lte
        duration_gte:
          anyOf:
          - type: number
          - type: 'null'
          title: Duration Gte
        duration_lte:
          anyOf:
          - type: number
          - type: 'null'
          title: Duration Lte
        pool:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Pool
        queue:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Queue
        executor:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Executor
        page_offset:
          type: integer
          minimum: 0.0
          title: Page Offset
          default: 0
        page_limit:
          type: integer
          minimum: 0.0
          title: Page Limit
          default: 100
        order_by:
          anyOf:
          - type: string
          - type: 'null'
          title: Order By
      additionalProperties: false
      type: object
      title: TaskInstancesBatchBody
      description: Task Instance body for get batch.
    TaskInstancesLogResponse:
      properties:
        content:
          anyOf:
          - items:
              $ref: '#/components/schemas/StructuredLogMessage'
            type: array
          - items:
              type: string
            type: array
          title: Content
        continuation_token:
          anyOf:
          - type: string
          - type: 'null'
          title: Continuation Token
      type: object
      required:
      - content
      - continuation_token
      title: TaskInstancesLogResponse
      description: Log serializer for responses.
    TaskOutletAssetReference:
      properties:
        dag_id:
          type: string
          title: Dag Id
        task_id:
          type: string
          title: Task Id
        created_at:
          type: string
          format: date-time
          title: Created At
        updated_at:
          type: string
          format: date-time
          title: Updated At
      additionalProperties: false
      type: object
      required:
      - dag_id
      - task_id
      - created_at
      - updated_at
      title: TaskOutletAssetReference
      description: Task outlet reference serializer for assets.
    TaskResponse:
      properties:
        task_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Task Id
        task_display_name:
          anyOf:
          - type: string
          - type: 'null'
          title: Task Display Name
        owner:
          anyOf:
          - type: string
          - type: 'null'
          title: Owner
        start_date:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Start Date
        end_date:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: End Date
        trigger_rule:
          anyOf:
          - type: string
          - type: 'null'
          title: Trigger Rule
        depends_on_past:
          type: boolean
          title: Depends On Past
        wait_for_downstream:
          type: boolean
          title: Wait For Downstream
        retries:
          anyOf:
          - type: number
          - type: 'null'
          title: Retries
        queue:
          anyOf:
          - type: string
          - type: 'null'
          title: Queue
        pool:
          anyOf:
          - type: string
          - type: 'null'
          title: Pool
        pool_slots:
          anyOf:
          - type: number
          - type: 'null'
          title: Pool Slots
        execution_timeout:
          anyOf:
          - $ref: '#/components/schemas/TimeDelta'
          - type: 'null'
        retry_delay:
          anyOf:
          - $ref: '#/components/schemas/TimeDelta'
          - type: 'null'
        retry_exponential_backoff:
          type: boolean
          title: Retry Exponential Backoff
        priority_weight:
          anyOf:
          - type: number
          - type: 'null'
          title: Priority Weight
        weight_rule:
          anyOf:
          - type: string
          - type: 'null'
          title: Weight Rule
        ui_color:
          anyOf:
          - type: string
          - type: 'null'
          title: Ui Color
        ui_fgcolor:
          anyOf:
          - type: string
          - type: 'null'
          title: Ui Fgcolor
        template_fields:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Template Fields
        downstream_task_ids:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Downstream Task Ids
        doc_md:
          anyOf:
          - type: string
          - type: 'null'
          title: Doc Md
        operator_name:
          anyOf:
          - type: string
          - type: 'null'
          title: Operator Name
        params:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Params
        class_ref:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Class Ref
        is_mapped:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Is Mapped
        extra_links:
          items:
            type: string
          type: array
          title: Extra Links
          description: Extract and return extra_links.
          readOnly: true
      type: object
      required:
      - task_id
      - task_display_name
      - owner
      - start_date
      - end_date
      - trigger_rule
      - depends_on_past
      - wait_for_downstream
      - retries
      - queue
      - pool
      - pool_slots
      - execution_timeout
      - retry_delay
      - retry_exponential_backoff
      - priority_weight
      - weight_rule
      - ui_color
      - ui_fgcolor
      - template_fields
      - downstream_task_ids
      - doc_md
      - operator_name
      - params
      - class_ref
      - is_mapped
      - extra_links
      title: TaskResponse
      description: Task serializer for responses.
    TimeDelta:
      properties:
        __type:
          type: string
          title: Type
          default: TimeDelta
        days:
          type: integer
          title: Days
        seconds:
          type: integer
          title: Seconds
        microseconds:
          type: integer
          title: Microseconds
      type: object
      required:
      - days
      - seconds
      - microseconds
      title: TimeDelta
      description: TimeDelta can be used to interact with datetime.timedelta objects.
    TriggerDAGRunPostBody:
      properties:
        dag_run_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Dag Run Id
        data_interval_start:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Data Interval Start
        data_interval_end:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Data Interval End
        logical_date:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Logical Date
        run_after:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Run After
        conf:
          additionalProperties: true
          type: object
          title: Conf
        note:
          anyOf:
          - type: string
          - type: 'null'
          title: Note
      additionalProperties: false
      type: object
      required:
      - logical_date
      title: TriggerDAGRunPostBody
      description: Trigger DAG Run Serializer for POST body.
    TriggerResponse:
      properties:
        id:
          type: integer
          title: Id
        classpath:
          type: string
          title: Classpath
        kwargs:
          type: string
          title: Kwargs
        created_date:
          type: string
          format: date-time
          title: Created Date
        triggerer_id:
          anyOf:
          - type: integer
          - type: 'null'
          title: Triggerer Id
      type: object
      required:
      - id
      - classpath
      - kwargs
      - created_date
      - triggerer_id
      title: TriggerResponse
      description: Trigger serializer for responses.
    TriggererInfoResponse:
      properties:
        status:
          anyOf:
          - type: string
          - type: 'null'
          title: Status
        latest_triggerer_heartbeat:
          anyOf:
          - type: string
          - type: 'null'
          title: Latest Triggerer Heartbeat
      type: object
      required:
      - status
      - latest_triggerer_heartbeat
      title: TriggererInfoResponse
      description: Triggerer info serializer for responses.
    ValidationError:
      properties:
        loc:
          items:
            anyOf:
            - type: string
            - type: integer
          type: array
          title: Location
        msg:
          type: string
          title: Message
        type:
          type: string
          title: Error Type
      type: object
      required:
      - loc
      - msg
      - type
      title: ValidationError
    VariableBody:
      properties:
        key:
          type: string
          maxLength: 250
          title: Key
        value:
          $ref: '#/components/schemas/JsonValue'
        description:
          anyOf:
          - type: string
          - type: 'null'
          title: Description
      additionalProperties: false
      type: object
      required:
      - key
      - value
      title: VariableBody
      description: Variable serializer for bodies.
    VariableCollectionResponse:
      properties:
        variables:
          items:
            $ref: '#/components/schemas/VariableResponse'
          type: array
          title: Variables
        total_entries:
          type: integer
          title: Total Entries
      type: object
      required:
      - variables
      - total_entries
      title: VariableCollectionResponse
      description: Variable Collection serializer for responses.
    VariableResponse:
      properties:
        key:
          type: string
          title: Key
        value:
          type: string
          title: Value
        description:
          anyOf:
          - type: string
          - type: 'null'
          title: Description
        is_encrypted:
          type: boolean
          title: Is Encrypted
      type: object
      required:
      - key
      - value
      - description
      - is_encrypted
      title: VariableResponse
      description: Variable serializer for responses.
    VersionInfo:
      properties:
        version:
          type: string
          title: Version
        git_version:
          anyOf:
          - type: string
          - type: 'null'
          title: Git Version
      type: object
      required:
      - version
      - git_version
      title: VersionInfo
      description: Version information serializer for responses.
    XComCollectionResponse:
      properties:
        xcom_entries:
          items:
            $ref: '#/components/schemas/XComResponse'
          type: array
          title: Xcom Entries
        total_entries:
          type: integer
          title: Total Entries
      type: object
      required:
      - xcom_entries
      - total_entries
      title: XComCollectionResponse
      description: XCom Collection serializer for responses.
    XComCreateBody:
      properties:
        key:
          type: string
          title: Key
        value:
          title: Value
        map_index:
          type: integer
          title: Map Index
          default: -1
      additionalProperties: false
      type: object
      required:
      - key
      - value
      title: XComCreateBody
      description: Payload serializer for creating an XCom entry.
    XComResponse:
      properties:
        key:
          type: string
          title: Key
        timestamp:
          type: string
          format: date-time
          title: Timestamp
        logical_date:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Logical Date
        map_index:
          type: integer
          title: Map Index
        task_id:
          type: string
          title: Task Id
        dag_id:
          type: string
          title: Dag Id
        run_id:
          type: string
          title: Run Id
      type: object
      required:
      - key
      - timestamp
      - logical_date
      - map_index
      - task_id
      - dag_id
      - run_id
      title: XComResponse
      description: Serializer for a xcom item.
    XComResponseNative:
      properties:
        key:
          type: string
          title: Key
        timestamp:
          type: string
          format: date-time
          title: Timestamp
        logical_date:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Logical Date
        map_index:
          type: integer
          title: Map Index
        task_id:
          type: string
          title: Task Id
        dag_id:
          type: string
          title: Dag Id
        run_id:
          type: string
          title: Run Id
        value:
          title: Value
      type: object
      required:
      - key
      - timestamp
      - logical_date
      - map_index
      - task_id
      - dag_id
      - run_id
      - value
      title: XComResponseNative
      description: XCom response serializer with native return type.
    XComResponseString:
      properties:
        key:
          type: string
          title: Key
        timestamp:
          type: string
          format: date-time
          title: Timestamp
        logical_date:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Logical Date
        map_index:
          type: integer
          title: Map Index
        task_id:
          type: string
          title: Task Id
        dag_id:
          type: string
          title: Dag Id
        run_id:
          type: string
          title: Run Id
        value:
          anyOf:
          - type: string
          - type: 'null'
          title: Value
      type: object
      required:
      - key
      - timestamp
      - logical_date
      - map_index
      - task_id
      - dag_id
      - run_id
      - value
      title: XComResponseString
      description: XCom response serializer with string return type.
    XComUpdateBody:
      properties:
        value:
          title: Value
        map_index:
          type: integer
          title: Map Index
          default: -1
      additionalProperties: false
      type: object
      required:
      - value
      title: XComUpdateBody
      description: Payload serializer for updating an XCom entry.
  securitySchemes:
    OAuth2PasswordBearer:
      type: oauth2
      description: To authenticate Airflow API requests, clients must include a JWT
        (JSON Web Token) in the Authorization header of each request. This token is
        used to verify the identity of the client and ensure that they have the appropriate
        permissions to access the requested resources. You can use the endpoint ``POST
        /auth/token`` in order to generate a JWT token. Upon successful authentication,
        the server will issue a JWT token that contains the necessary information
        (such as user identity and scope) to authenticate subsequent requests. To
        learn more about Airflow public API authentication, please read https://airflow.apache.org/docs/apache-airflow/stable/security/api.html.
      flows:
        password:
          scopes: {}
          tokenUrl: /auth/token
