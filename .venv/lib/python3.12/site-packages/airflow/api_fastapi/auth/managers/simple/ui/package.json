{"name": "simple-auth-manager-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 5174", "build": "vite build", "lint": "eslint --quiet && tsc --p tsconfig.app.json", "lint:fix": "eslint --fix && tsc --p tsconfig.app.json", "format": "pnpm prettier --write .", "preview": "vite preview", "codegen": "openapi-rq -i \"../openapi/v2-simple-auth-manager-generated.yaml\" -c axios --format prettier -o openapi-gen --operationId", "test": "vitest run", "coverage": "vitest run --coverage"}, "dependencies": {"@chakra-ui/react": "^3.14.2", "@tanstack/react-query": "^5.70.0", "axios": "^1.11.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-cookie": "^8.0.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-router-dom": "^7.5.2"}, "devDependencies": {"@7nohe/openapi-react-query-codegen": "^1.6.2", "@eslint/compat": "^1.2.7", "@eslint/js": "^9.23.0", "@stylistic/eslint-plugin": "^2.13.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/react": "^18.3.19", "@types/react-dom": "^19.0.0", "@vitejs/plugin-react-swc": "^3.8.1", "eslint": "^9.23.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-perfectionist": "^4.10.1", "eslint-plugin-prettier": "^5.2.5", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-unicorn": "^55.0.0", "happy-dom": "^17.4.4", "prettier": "^3.5.3", "typescript": "~5.5.4", "typescript-eslint": "^8.27.0", "vite": "^6.2.6", "vite-plugin-css-injected-by-js": "^3.5.2", "vitest": "^3.0.9"}}