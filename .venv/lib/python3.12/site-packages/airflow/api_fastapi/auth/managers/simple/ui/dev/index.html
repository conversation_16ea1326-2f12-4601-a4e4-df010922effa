<!-- Entry html file when the Vite dev server is running -->
<!doctype html>
<html lang="en" style="height: 100%">
  <head>
    <meta charset="UTF-8" />
    <base href="{{ backend_server_base_url }}" />
    <link rel="icon" type="image/png" href="http://localhost:5174/pin_32.png" />
    <script type="module" src="http://localhost:5174/@vite/client"></script>
    <script type="module">
      import RefreshRuntime from "http://localhost:5174/@react-refresh";

      RefreshRuntime.injectIntoGlobalHook(window);
      window.$RefreshReg$ = () => {};
      window.$RefreshSig$ = () => (type) => type;
      window.__vite_plugin_react_preamble_installed__ = true;
    </script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Airflow</title>
  </head>
  <body style="height: 100%">
    <div id="root" style="height: 100%"></div>
    <script type="module" src="http://localhost:5174/src/main.tsx"></script>
  </body>
</html>
