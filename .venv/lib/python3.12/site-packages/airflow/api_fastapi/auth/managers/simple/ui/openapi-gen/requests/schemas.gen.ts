// This file is auto-generated by @hey-api/openapi-ts

export const $HTTPExceptionResponse = {
  properties: {
    detail: {
      anyOf: [
        {
          type: "string",
        },
        {
          additionalProperties: true,
          type: "object",
        },
      ],
      title: "Detail",
    },
  },
  type: "object",
  required: ["detail"],
  title: "HTTPExceptionResponse",
  description: "HTTPException Model used for error response.",
} as const;

export const $HTTPValidationError = {
  properties: {
    detail: {
      items: {
        $ref: "#/components/schemas/ValidationError",
      },
      type: "array",
      title: "Detail",
    },
  },
  type: "object",
  title: "HTTPValidationError",
} as const;

export const $LoginBody = {
  properties: {
    username: {
      type: "string",
      title: "Username",
    },
    password: {
      type: "string",
      title: "Password",
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["username", "password"],
  title: "LoginBody",
  description: "Login serializer for post bodies.",
} as const;

export const $LoginResponse = {
  properties: {
    access_token: {
      type: "string",
      title: "Access Token",
    },
  },
  type: "object",
  required: ["access_token"],
  title: "LoginResponse",
  description: "Login serializer for responses.",
} as const;

export const $ValidationError = {
  properties: {
    loc: {
      items: {
        anyOf: [
          {
            type: "string",
          },
          {
            type: "integer",
          },
        ],
      },
      type: "array",
      title: "Location",
    },
    msg: {
      type: "string",
      title: "Message",
    },
    type: {
      type: "string",
      title: "Error Type",
    },
  },
  type: "object",
  required: ["loc", "msg", "type"],
  title: "ValidationError",
} as const;
