#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

"""
remove scheduler_lock column.

Revision ID: 486ac7936b78
Revises: d59cbbef95eb
Create Date: 2024-10-23 07:48:52.494396

"""

from __future__ import annotations

import sqlalchemy as sa
from alembic import op

revision = "486ac7936b78"
down_revision = "d59cbbef95eb"
branch_labels = None
depends_on = None
airflow_version = "3.0.0"


def upgrade():
    """Apply remove scheduler_lock column."""
    with op.batch_alter_table("dag", schema=None) as batch_op:
        batch_op.drop_column("scheduler_lock")


def downgrade():
    """Unapply remove scheduler_lock column."""
    with op.batch_alter_table("dag", schema=None) as batch_op:
        batch_op.add_column(sa.Column("scheduler_lock", sa.BOOLEAN(), autoincrement=False, nullable=True))
