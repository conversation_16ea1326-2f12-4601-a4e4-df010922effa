/*!
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
import { FiDatabase } from "react-icons/fi";

import type { AssetResponse } from "openapi/requests/types.gen";
import { HeaderCard } from "src/components/HeaderCard";

import { DependencyPopover } from "../AssetsList/DependencyPopover";

export const Header = ({
  asset,
  isRefreshing,
}: {
  readonly asset?: AssetResponse;
  readonly isRefreshing?: boolean;
}) => {
  const stats = [
    { label: "Group", value: asset?.group },
    {
      label: "Producing Tasks",
      value: <DependencyPopover dependencies={asset?.producing_tasks ?? []} type="Task" />,
    },
    {
      label: "Consuming Dags",
      value: <DependencyPopover dependencies={asset?.consuming_dags ?? []} type="Dag" />,
    },
  ];

  return <HeaderCard icon={<FiDatabase />} isRefreshing={isRefreshing} stats={stats} title={asset?.name} />;
};
