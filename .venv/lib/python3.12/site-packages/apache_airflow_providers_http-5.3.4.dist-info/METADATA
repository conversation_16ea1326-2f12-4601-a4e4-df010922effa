Metadata-Version: 2.4
Name: apache-airflow-providers-http
Version: 5.3.4
Summary: Provider package apache-airflow-providers-http for Apache Airflow
Keywords: airflow-provider,http,airflow,integration
Author-email: Apache Software Foundation <<EMAIL>>
Maintainer-email: Apache Software Foundation <<EMAIL>>
Requires-Python: >=3.10
Description-Content-Type: text/x-rst
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: System Administrators
Classifier: Framework :: Apache Airflow
Classifier: Framework :: Apache Airflow :: Provider
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: System :: Monitoring
Requires-Dist: apache-airflow>=2.10.0
Requires-Dist: requests>=2.32.0,<3
Requires-Dist: requests-toolbelt>=1.0.0
Requires-Dist: aiohttp>=3.12.14
Requires-Dist: asgiref>=2.3.0
Project-URL: Bug Tracker, https://github.com/apache/airflow/issues
Project-URL: Changelog, https://airflow.apache.org/docs/apache-airflow-providers-http/5.3.4/changelog.html
Project-URL: Documentation, https://airflow.apache.org/docs/apache-airflow-providers-http/5.3.4
Project-URL: Mastodon, https://fosstodon.org/@airflow
Project-URL: Slack Chat, https://s.apache.org/airflow-slack
Project-URL: Source Code, https://github.com/apache/airflow
Project-URL: YouTube, https://www.youtube.com/channel/UCSXwxpWZQ7XZ1WL3wqevChA/


.. Licensed to the Apache Software Foundation (ASF) under one
   or more contributor license agreements.  See the NOTICE file
   distributed with this work for additional information
   regarding copyright ownership.  The ASF licenses this file
   to you under the Apache License, Version 2.0 (the
   "License"); you may not use this file except in compliance
   with the License.  You may obtain a copy of the License at

..   http://www.apache.org/licenses/LICENSE-2.0

.. Unless required by applicable law or agreed to in writing,
   software distributed under the License is distributed on an
   "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
   KIND, either express or implied.  See the License for the
   specific language governing permissions and limitations
   under the License.

.. NOTE! THIS FILE IS AUTOMATICALLY GENERATED AND WILL BE OVERWRITTEN!

.. IF YOU WANT TO MODIFY TEMPLATE FOR THIS FILE, YOU SHOULD MODIFY THE TEMPLATE
   ``PROVIDER_README_TEMPLATE.rst.jinja2`` IN the ``dev/breeze/src/airflow_breeze/templates`` DIRECTORY

Package ``apache-airflow-providers-http``

Release: ``5.3.4``


`Hypertext Transfer Protocol (HTTP) <https://www.w3.org/Protocols/>`__


Provider package
----------------

This is a provider package for ``http`` provider. All classes for this provider package
are in ``airflow.providers.http`` python package.

You can find package information and changelog for the provider
in the `documentation <https://airflow.apache.org/docs/apache-airflow-providers-http/5.3.4/>`_.

Installation
------------

You can install this package on top of an existing Airflow installation (see ``Requirements`` below
for the minimum Airflow version supported) via
``pip install apache-airflow-providers-http``

The package supports the following python versions: 3.10,3.11,3.12,3.13

Requirements
------------

=====================  ==================
PIP package            Version required
=====================  ==================
``apache-airflow``     ``>=2.10.0``
``requests``           ``>=2.32.0,<3``
``requests-toolbelt``  ``>=1.0.0``
``aiohttp``            ``>=3.12.14``
``asgiref``            ``>=2.3.0``
=====================  ==================

The changelog for the provider package can be found in the
`changelog <https://airflow.apache.org/docs/apache-airflow-providers-http/5.3.4/changelog.html>`_.

