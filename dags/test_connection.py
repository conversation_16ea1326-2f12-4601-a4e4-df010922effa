# File: ~/Projects/my_airflow_project/dags/test_sharepoint_connection.py

import json
from datetime import datetime

from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator
from airflow.providers.http.hooks.http import HttpHook
import requests

def test_graph_api_connection():
    """
    This function fetches the connection, gets an OAuth token, and makes a test API call.
    """
    print("Fetching connection details from Airflow...")
    conn = HttpHook.get_connection('sharepoint_graph_api')
    credentials = conn.extra_dejson
    client_id = credentials.get('client_id')
    client_secret = credentials.get('client_secret')
    tenant_id = credentials.get('tenant_id')

    if not all([client_id, client_secret, tenant_id]):
        raise ValueError("client_id, client_secret, and tenant_id must be set in the connection's Extra field.")

    print(f"Successfully retrieved credentials for tenant: {tenant_id}")

    # Authenticate with Microsoft Graph to get an access token
    token_url = f"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/token"
    token_payload = {
        'grant_type': 'client_credentials',
        'client_id': client_id,
        'client_secret': client_secret,
        'scope': 'https://graph.microsoft.com/.default'
    }

    print("Requesting access token...")
    try:
        token_response = requests.post(token_url, data=token_payload)
        token_response.raise_for_status() 
        access_token = token_response.json().get('access_token')
        if not access_token:
            raise ValueError("Failed to get access token. Check your credentials and API permissions.")
        print("Successfully obtained access token!")
    except requests.exceptions.HTTPError as e:
        print(f"Error getting token: {e}")
        print(f"Response body: {token_response.text}")
        raise

    # Use the access token to make a test API call
    print("Making a test API call to graph.microsoft.com...")
    http_hook = HttpHook(method='GET', http_conn_id='sharepoint_graph_api')
    
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    endpoint = 'v1.0/users?$top=1'
    api_response = http_hook.run(endpoint=endpoint, headers=headers)
    
    print("API Call Successful!")
    print(f"Response: {api_response.text[:300]}...")

with DAG(
    dag_id='test_sharepoint_connection_from_project', # Changed ID slightly to avoid confusion
    start_date=datetime(2023, 1, 1),
    schedule=None,
    catchup=False,
    tags=['test', 'graph-api', 'project'],
) as dag:
    test_connection_task = PythonOperator(
        task_id='test_graph_api_authentication',
        python_callable=test_graph_api_connection,
    )